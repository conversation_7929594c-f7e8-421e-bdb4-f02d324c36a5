{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/locales/ar.ts"], "sourcesContent": ["// Arabic translations for the nursery app\nexport const ar = {\n  // Common\n  common: {\n    loading: \"جاري التحميل...\",\n    save: \"حفظ\",\n    cancel: \"إلغاء\",\n    delete: \"حذف\",\n    edit: \"تعديل\",\n    add: \"إضافة\",\n    submit: \"إرسال\",\n    back: \"رجوع\",\n    next: \"التالي\",\n    previous: \"السابق\",\n    close: \"إغلاق\",\n    confirm: \"تأكيد\",\n    yes: \"نعم\",\n    no: \"لا\",\n    search: \"بحث\",\n    filter: \"تصفية\",\n    all: \"الكل\",\n    none: \"لا يوجد\",\n    required: \"مطلوب\",\n    optional: \"اختياري\",\n    success: \"نجح\",\n    error: \"خطأ\",\n    warning: \"تحذير\",\n    info: \"معلومات\",\n  },\n\n  // Navigation\n  nav: {\n    home: \"الرئيسية\",\n    account: \"حسابي\",\n    permissions: \"التصاريح\",\n    children: \"الأطفال\",\n    profile: \"الملف الشخصي\",\n    settings: \"الإعدادات\",\n    admin: \"لوحة الإدارة\",\n    logout: \"تسجيل الخروج\",\n    login: \"تسجيل الدخول\",\n    register: \"إنشاء حساب\",\n  },\n\n  // Authentication\n  auth: {\n    login: {\n      title: \"تسجيل الدخول إلى استيراد وتصدير\",\n      subtitle: \"مرحباً بك في نظام إدارة التصاريح\",\n      email: \"البريد الإلكتروني\",\n      password: \"كلمة المرور\",\n      rememberMe: \"تذكرني\",\n      forgotPassword: \"نسيت كلمة المرور؟\",\n      loginButton: \"تسجيل الدخول\",\n      noAccount: \"ليس لديك حساب؟\",\n      createAccount: \"إنشاء حساب جديد\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      passwordPlaceholder: \"أدخل كلمة المرور\",\n    },\n    register: {\n      title: \"إنشاء حساب جديد\",\n      subtitle: \"انضم إلى عائلة استيراد وتصدير\",\n      firstName: \"الاسم الأول\",\n      lastName: \"اسم العائلة\",\n      email: \"البريد الإلكتروني\",\n      password: \"كلمة المرور\",\n      confirmPassword: \"تأكيد كلمة المرور\",\n      phone: \"رقم الهاتف\",\n      city: \"المدينة\",\n      governorate: \"المحافظة\",\n      registerButton: \"إنشاء الحساب\",\n      haveAccount: \"لديك حساب بالفعل؟\",\n      loginHere: \"سجل دخولك هنا\",\n      firstNamePlaceholder: \"أدخل اسمك الأول\",\n      lastNamePlaceholder: \"أدخل اسم العائلة\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      passwordPlaceholder: \"أدخل كلمة مرور قوية\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور\",\n      phonePlaceholder: \"أدخل رقم هاتفك\",\n      cityPlaceholder: \"أدخل مدينتك\",\n      governoratePlaceholder: \"اختر محافظتك\",\n    },\n    activation: {\n      title: \"تفعيل الحساب\",\n      subtitle: \"أدخل رمز التفعيل المرسل إلى بريدك الإلكتروني\",\n      code: \"رمز التفعيل\",\n      codePlaceholder: \"أدخل الرمز المكون من 6 أرقام\",\n      activateButton: \"تفعيل الحساب\",\n      resendCode: \"إعادة إرسال الرمز\",\n      changeEmail: \"تغيير البريد الإلكتروني\",\n    },\n    forgotPassword: {\n      title: \"استعادة كلمة المرور\",\n      subtitle: \"أدخل بريدك الإلكتروني لاستعادة كلمة المرور\",\n      email: \"البريد الإلكتروني\",\n      emailPlaceholder: \"أدخل بريدك الإلكتروني\",\n      sendCode: \"إرسال رمز الاستعادة\",\n      backToLogin: \"العودة لتسجيل الدخول\",\n    },\n    resetPassword: {\n      title: \"إعادة تعيين كلمة المرور\",\n      subtitle: \"أدخل الرمز وكلمة المرور الجديدة\",\n      code: \"رمز الاستعادة\",\n      newPassword: \"كلمة المرور الجديدة\",\n      confirmPassword: \"تأكيد كلمة المرور الجديدة\",\n      codePlaceholder: \"أدخل رمز الاستعادة\",\n      newPasswordPlaceholder: \"أدخل كلمة المرور الجديدة\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور الجديدة\",\n      resetButton: \"إعادة تعيين كلمة المرور\",\n    },\n  },\n\n  // Account\n  account: {\n    title: \"حسابي\",\n    subtitle: \"إدارة معلوماتك الشخصية وتصاريح الأطفال\",\n    profile: {\n      title: \"الملف الشخصي\",\n      subtitle: \"معلوماتك الشخصية\",\n      personalInfo: \"المعلومات الشخصية\",\n      contactInfo: \"معلومات الاتصال\",\n      location: \"الموقع\",\n      accountStatus: \"حالة الحساب\",\n      approved: \"مُعتمد\",\n      pending: \"في الانتظار\",\n      emailVerified: \"البريد مُفعل\",\n      emailNotVerified: \"البريد غير مُفعل\",\n      admin: \"مدير\",\n      joinedDate: \"تاريخ الانضمام\",\n    },\n    children: {\n      title: \"الأطفال\",\n      subtitle: \"إدارة معلومات أطفالك\",\n      addChild: \"إضافة طفل\",\n      noChildren: \"لم تتم إضافة أطفال بعد\",\n      addFirstChild: \"أضف طفلك الأول لبدء إنشاء التصاريح\",\n      childName: \"اسم الطفل\",\n      childClass: \"الفصل\",\n      childStage: \"المرحلة\",\n      childNamePlaceholder: \"أدخل اسم الطفل\",\n      childClassPlaceholder: \"أدخل فصل الطفل\",\n      childStagePlaceholder: \"اختر مرحلة الطفل\",\n      stages: {\n        nursery: \"حضانة\",\n        kg1: \"روضة أولى\",\n        kg2: \"روضة ثانية\",\n      },\n    },\n    permissions: {\n      title: \"التصاريح\",\n      subtitle: \"إدارة تصاريح استلام الأطفال\",\n      addPermission: \"إضافة تصريح\",\n      noPermissions: \"لم يتم إنشاء تصاريح بعد\",\n      createFirstPermission: \"أنشئ تصريحك الأول للسماح لشخص باستلام طفلك\",\n      selectChild: \"اختر الطفل\",\n      receiverName: \"اسم المستلم\",\n      receiverNamePlaceholder: \"أدخل اسم الشخص الذي سيستلم الطفل\",\n      idImage: \"صورة الهوية\",\n      idImagePlaceholder: \"اسحب وأفلت صورة هوية المستلم هنا أو انقر للاختيار\",\n      createPermission: \"إنشاء التصريح\",\n      otp: \"رمز التصريح\",\n      status: {\n        pending: \"في الانتظار\",\n        approved: \"مُعتمد\",\n        declined: \"مرفوض\",\n        expired: \"منتهي الصلاحية\",\n      },\n      createdAt: \"تاريخ الإنشاء\",\n      expiresAt: \"تاريخ انتهاء الصلاحية\",\n      deleteConfirm: \"هل أنت متأكد من حذف التصريح لـ {name}؟ لا يمكن التراجع عن هذا الإجراء.\",\n    },\n    password: {\n      title: \"تغيير كلمة المرور\",\n      subtitle: \"تحديث كلمة مرور حسابك\",\n      currentPassword: \"كلمة المرور الحالية\",\n      newPassword: \"كلمة المرور الجديدة\",\n      confirmPassword: \"تأكيد كلمة المرور الجديدة\",\n      currentPasswordPlaceholder: \"أدخل كلمة المرور الحالية\",\n      newPasswordPlaceholder: \"أدخل كلمة المرور الجديدة\",\n      confirmPasswordPlaceholder: \"أعد إدخال كلمة المرور الجديدة\",\n      changePassword: \"تغيير كلمة المرور\",\n      strength: {\n        weak: \"ضعيفة\",\n        medium: \"متوسطة\",\n        strong: \"قوية\",\n      },\n    },\n  },\n\n  // Admin\n  admin: {\n    title: \"لوحة الإدارة\",\n    subtitle: \"إدارة النظام\",\n    users: {\n      title: \"إدارة المستخدمين\",\n      subtitle: \"إدارة حسابات المستخدمين والموافقات\",\n      totalUsers: \"إجمالي المستخدمين\",\n      pendingApproval: \"في انتظار الموافقة\",\n      approvedUsers: \"المستخدمون المُعتمدون\",\n      approve: \"اعتماد\",\n      delete: \"حذف\",\n      userDetails: \"تفاصيل المستخدم\",\n      contactInfo: \"معلومات الاتصال\",\n      children: \"الأطفال\",\n      noChildren: \"لا يوجد أطفال مسجلون\",\n      approvalInfo: \"معلومات الاعتماد\",\n      approvedBy: \"اعتمد بواسطة\",\n      approvedOn: \"تاريخ الاعتماد\",\n    },\n    permissions: {\n      title: \"إدارة التصاريح\",\n      subtitle: \"الموافقة على تصاريح استلام الأطفال أو رفضها\",\n      allPermissions: \"جميع التصاريح\",\n      pendingPermissions: \"التصاريح المعلقة\",\n      approvedPermissions: \"التصاريح المُعتمدة\",\n      declinedPermissions: \"التصاريح المرفوضة\",\n      expiredPermissions: \"التصاريح المنتهية\",\n      approve: \"اعتماد\",\n      decline: \"رفض\",\n      parent: \"ولي الأمر\",\n      child: \"الطفل\",\n      receiver: \"المستلم\",\n      created: \"تاريخ الإنشاء\",\n      expires: \"تاريخ انتهاء الصلاحية\",\n      approvedBy: \"اعتمد بواسطة\",\n      approvedOn: \"تاريخ الاعتماد\",\n    },\n    settings: {\n      title: \"إعدادات النظام\",\n      subtitle: \"إعدادات إدارة النظام\",\n      systemMaintenance: \"صيانة النظام\",\n      cleanupExpired: \"تنظيف التصاريح المنتهية\",\n      cleanupDescription: \"تنظيف التصاريح المنتهية والحفاظ على صحة النظام\",\n      adminInfo: \"معلومات المدير\",\n      adminType: \"نوع المدير\",\n      superAdmin: \"مدير عام\",\n      normalAdmin: \"مدير عادي\",\n      superAdminDescription: \"لديك صلاحيات إدارية كاملة تشمل إدارة المستخدمين واعتماد/رفض التصاريح وصيانة النظام\",\n      normalAdminDescription: \"لديك صلاحيات إدارية محدودة. يمكنك عرض التصاريح المُعتمدة بالتفصيل الكامل ولكن لا يمكنك إدارة المستخدمين أو اعتماد/رفض التصاريح\",\n    },\n  },\n\n  // Nursery specific\n  nursery: {\n    name: \"استيراد وتصدير\",\n    tagline: \"مكان آمن لنمو أطفالكم\",\n    welcome: \"مرحباً بكم في استيراد وتصدير\",\n    description: \"نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان والمتابعة\",\n    features: {\n      safety: \"الأمان أولاً\",\n      education: \"تعليم متميز\",\n      care: \"رعاية شاملة\",\n      technology: \"تقنية حديثة\",\n    },\n  },\n\n  // Pages\n  pages: {\n    home: {\n      hero: {\n        title: \"مرحباً بكم في استيراد وتصدير\",\n        subtitle: \"نوفر بيئة تعليمية آمنة ومحبة لأطفالكم مع أحدث أنظمة الأمان والمتابعة\",\n        getStarted: \"ابدأ الآن\",\n        learnMore: \"اعرف المزيد\",\n      },\n      features: {\n        title: \"لماذا تختار استيراد وتصدير؟\",\n        subtitle: \"نوفر أفضل الخدمات والمميزات لضمان راحة وأمان أطفالكم\",\n        safety: {\n          title: \"الأمان أولاً\",\n          description: \"نظام أمان متطور مع تصاريح إلكترونية وتتبع دقيق لضمان سلامة الأطفال\",\n        },\n        education: {\n          title: \"تعليم متميز\",\n          description: \"برامج تعليمية حديثة ومتطورة تناسب جميع المراحل العمرية للأطفال\",\n        },\n        care: {\n          title: \"رعاية شاملة\",\n          description: \"رعاية شاملة ومحبة من فريق مؤهل ومدرب على أعلى المستويات\",\n        },\n        technology: {\n          title: \"تقنية حديثة\",\n          description: \"تقنيات حديثة لمتابعة تطور الأطفال والتواصل المستمر مع الأهالي\",\n        },\n      },\n      howItWorks: {\n        title: \"كيف يعمل نظام التصاريح؟\",\n        subtitle: \"خطوات بسيطة لضمان أمان استلام أطفالكم\",\n        step1: {\n          title: \"إنشاء حساب\",\n          description: \"سجل حسابك وأضف معلومات أطفالك بشكل آمن\",\n        },\n        step2: {\n          title: \"إنشاء تصريح\",\n          description: \"أنشئ تصريح استلام مع صورة هوية الشخص المخول\",\n        },\n        step3: {\n          title: \"استلام آمن\",\n          description: \"استخدم رمز التصريح لاستلام آمن ومؤكد\",\n        },\n      },\n      stats: {\n        families: \"عائلة سعيدة\",\n        children: \"طفل محبوب\",\n        satisfaction: \"رضا الأهالي\",\n        support: \"دعم مستمر\",\n      },\n    },\n  },\n\n  // Messages\n  messages: {\n    success: {\n      loginSuccess: \"تم تسجيل الدخول بنجاح\",\n      registerSuccess: \"تم إنشاء الحساب بنجاح\",\n      activationSuccess: \"تم تفعيل الحساب بنجاح\",\n      passwordChanged: \"تم تغيير كلمة المرور بنجاح\",\n      profileUpdated: \"تم تحديث الملف الشخصي بنجاح\",\n      childAdded: \"تم إضافة الطفل بنجاح\",\n      childUpdated: \"تم تحديث معلومات الطفل بنجاح\",\n      childDeleted: \"تم حذف الطفل بنجاح\",\n      permissionCreated: \"تم إنشاء التصريح بنجاح\",\n      permissionDeleted: \"تم حذف التصريح بنجاح\",\n      permissionApproved: \"تم اعتماد التصريح بنجاح\",\n      permissionDeclined: \"تم رفض التصريح بنجاح\",\n      userApproved: \"تم اعتماد المستخدم بنجاح\",\n      userDeleted: \"تم حذف المستخدم بنجاح\",\n    },\n    error: {\n      loginFailed: \"فشل في تسجيل الدخول\",\n      registerFailed: \"فشل في إنشاء الحساب\",\n      activationFailed: \"فشل في تفعيل الحساب\",\n      passwordChangeFailed: \"فشل في تغيير كلمة المرور\",\n      profileUpdateFailed: \"فشل في تحديث الملف الشخصي\",\n      childAddFailed: \"فشل في إضافة الطفل\",\n      childUpdateFailed: \"فشل في تحديث معلومات الطفل\",\n      childDeleteFailed: \"فشل في حذف الطفل\",\n      permissionCreateFailed: \"فشل في إنشاء التصريح\",\n      permissionDeleteFailed: \"فشل في حذف التصريح\",\n      permissionApproveFailed: \"فشل في اعتماد التصريح\",\n      permissionDeclineFailed: \"فشل في رفض التصريح\",\n      userApproveFailed: \"فشل في اعتماد المستخدم\",\n      userDeleteFailed: \"فشل في حذف المستخدم\",\n      accessDenied: \"تم رفض الوصول\",\n      adminRequired: \"مطلوب صلاحيات إدارية\",\n      superAdminRequired: \"مطلوب صلاحيات مدير عام\",\n      emailNotVerified: \"البريد الإلكتروني غير مُفعل\",\n      accountNotApproved: \"الحساب غير مُعتمد\",\n      networkError: \"خطأ في الشبكة\",\n      serverError: \"خطأ في الخادم\",\n      unknownError: \"خطأ غير معروف\",\n    },\n  },\n\n  // Validation\n  validation: {\n    required: \"هذا الحقل مطلوب\",\n    email: \"يرجى إدخال بريد إلكتروني صحيح\",\n    password: \"كلمة المرور يجب أن تكون 6 أحرف على الأقل\",\n    passwordMatch: \"كلمات المرور غير متطابقة\",\n    phone: \"يرجى إدخال رقم هاتف صحيح\",\n    minLength: \"يجب أن يكون {min} أحرف على الأقل\",\n    maxLength: \"يجب أن يكون {max} أحرف كحد أقصى\",\n    numeric: \"يجب أن يحتوي على أرقام فقط\",\n    alphanumeric: \"يجب أن يحتوي على أحرف وأرقام فقط\",\n  },\n};\n\nexport type TranslationKey = keyof typeof ar;\nexport default ar;\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AACnC,MAAM,KAAK;IAChB,SAAS;IACT,QAAQ;QACN,SAAS;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,KAAK;QACL,QAAQ;QACR,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,SAAS;QACT,KAAK;QACL,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;QACN,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,aAAa;IACb,KAAK;QACH,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IAEA,iBAAiB;IACjB,MAAM;QACJ,OAAO;YACL,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,WAAW;YACX,eAAe;YACf,kBAAkB;YAClB,qBAAqB;QACvB;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,WAAW;YACX,UAAU;YACV,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,OAAO;YACP,MAAM;YACN,aAAa;YACb,gBAAgB;YAChB,aAAa;YACb,WAAW;YACX,sBAAsB;YACtB,qBAAqB;YACrB,kBAAkB;YAClB,qBAAqB;YACrB,4BAA4B;YAC5B,kBAAkB;YAClB,iBAAiB;YACjB,wBAAwB;QAC1B;QACA,YAAY;YACV,OAAO;YACP,UAAU;YACV,MAAM;YACN,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,aAAa;QACf;QACA,gBAAgB;YACd,OAAO;YACP,UAAU;YACV,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,aAAa;QACf;QACA,eAAe;YACb,OAAO;YACP,UAAU;YACV,MAAM;YACN,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,wBAAwB;YACxB,4BAA4B;YAC5B,aAAa;QACf;IACF;IAEA,UAAU;IACV,SAAS;QACP,OAAO;QACP,UAAU;QACV,SAAS;YACP,OAAO;YACP,UAAU;YACV,cAAc;YACd,aAAa;YACb,UAAU;YACV,eAAe;YACf,UAAU;YACV,SAAS;YACT,eAAe;YACf,kBAAkB;YAClB,OAAO;YACP,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,UAAU;YACV,YAAY;YACZ,eAAe;YACf,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,sBAAsB;YACtB,uBAAuB;YACvB,uBAAuB;YACvB,QAAQ;gBACN,SAAS;gBACT,KAAK;gBACL,KAAK;YACP;QACF;QACA,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;YACf,uBAAuB;YACvB,aAAa;YACb,cAAc;YACd,yBAAyB;YACzB,SAAS;YACT,oBAAoB;YACpB,kBAAkB;YAClB,KAAK;YACL,QAAQ;gBACN,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,SAAS;YACX;YACA,WAAW;YACX,WAAW;YACX,eAAe;QACjB;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,aAAa;YACb,iBAAiB;YACjB,4BAA4B;YAC5B,wBAAwB;YACxB,4BAA4B;YAC5B,gBAAgB;YAChB,UAAU;gBACR,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF;IACF;IAEA,QAAQ;IACR,OAAO;QACL,OAAO;QACP,UAAU;QACV,OAAO;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,SAAS;YACT,QAAQ;YACR,aAAa;YACb,aAAa;YACb,UAAU;YACV,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA,aAAa;YACX,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;YACpB,SAAS;YACT,SAAS;YACT,QAAQ;YACR,OAAO;YACP,UAAU;YACV,SAAS;YACT,SAAS;YACT,YAAY;YACZ,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,mBAAmB;YACnB,gBAAgB;YAChB,oBAAoB;YACpB,WAAW;YACX,WAAW;YACX,YAAY;YACZ,aAAa;YACb,uBAAuB;YACvB,wBAAwB;QAC1B;IACF;IAEA,mBAAmB;IACnB,SAAS;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,aAAa;QACb,UAAU;YACR,QAAQ;YACR,WAAW;YACX,MAAM;YACN,YAAY;QACd;IACF;IAEA,QAAQ;IACR,OAAO;QACL,MAAM;YACJ,MAAM;gBACJ,OAAO;gBACP,UAAU;gBACV,YAAY;gBACZ,WAAW;YACb;YACA,UAAU;gBACR,OAAO;gBACP,UAAU;gBACV,QAAQ;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA,WAAW;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,YAAY;oBACV,OAAO;oBACP,aAAa;gBACf;YACF;YACA,YAAY;gBACV,OAAO;gBACP,UAAU;gBACV,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;YACF;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,SAAS;YACX;QACF;IACF;IAEA,WAAW;IACX,UAAU;QACR,SAAS;YACP,cAAc;YACd,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;YACjB,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,cAAc;YACd,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,cAAc;YACd,aAAa;QACf;QACA,OAAO;YACL,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;YACtB,qBAAqB;YACrB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,wBAAwB;YACxB,wBAAwB;YACxB,yBAAyB;YACzB,yBAAyB;YACzB,mBAAmB;YACnB,kBAAkB;YAClB,cAAc;YACd,eAAe;YACf,oBAAoB;YACpB,kBAAkB;YAClB,oBAAoB;YACpB,cAAc;YACd,aAAa;YACb,cAAc;QAChB;IACF;IAEA,aAAa;IACb,YAAY;QACV,UAAU;QACV,OAAO;QACP,UAAU;QACV,eAAe;QACf,OAAO;QACP,WAAW;QACX,WAAW;QACX,SAAS;QACT,cAAc;IAChB;AACF;uCAGe", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/hooks/useTranslation.ts"], "sourcesContent": ["import { ar } from '@/locales/ar';\n\n// Type for nested object keys\ntype NestedKeyOf<ObjectType extends object> = {\n  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object\n    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`\n    : `${Key}`;\n}[keyof ObjectType & (string | number)];\n\ntype TranslationKey = NestedKeyOf<typeof ar>;\n\n// Function to get nested value from object using dot notation\nfunction getNestedValue(obj: any, path: string): string {\n  return path.split('.').reduce((current, key) => current?.[key], obj) || path;\n}\n\n// Function to replace placeholders in translation strings\nfunction replacePlaceholders(text: string, params: Record<string, string | number> = {}): string {\n  return text.replace(/\\{(\\w+)\\}/g, (match, key) => {\n    return params[key]?.toString() || match;\n  });\n}\n\nexport function useTranslation() {\n  const t = (key: TranslationKey, params?: Record<string, string | number>): string => {\n    const translation = getNestedValue(ar, key);\n    return replacePlaceholders(translation, params);\n  };\n\n  return { t };\n}\n\nexport default useTranslation;\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,8DAA8D;AAC9D,SAAS,eAAe,GAAQ,EAAE,IAAY;IAC5C,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,MAAQ,SAAS,CAAC,IAAI,EAAE,QAAQ;AAC1E;AAEA,0DAA0D;AAC1D,SAAS,oBAAoB,IAAY,EAAE,SAA0C,CAAC,CAAC;IACrF,OAAO,KAAK,OAAO,CAAC,cAAc,CAAC,OAAO;QACxC,OAAO,MAAM,CAAC,IAAI,EAAE,cAAc;IACpC;AACF;AAEO,SAAS;IACd,MAAM,IAAI,CAAC,KAAqB;QAC9B,MAAM,cAAc,eAAe,oHAAA,CAAA,KAAE,EAAE;QACvC,OAAO,oBAAoB,aAAa;IAC1C;IAEA,OAAO;QAAE;IAAE;AACb;uCAEe", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from \"react\";\nimport {\n  User,\n  Settings,\n  Lock,\n  MapPin,\n  Users,\n  CreditCard,\n  Package,\n  FileText,\n  DollarSign,\n  Receipt,\n  ChevronDown,\n  ChevronUp,\n} from \"lucide-react\";\nimport BalanceDisplay from \"./BalanceDisplay\";\n\ninterface SidebarItem {\n  label: string;\n  href: string;\n  icon: React.ReactNode;\n  adminOnly?: boolean;\n  group?: string;\n}\n\ninterface SidebarGroup {\n  label: string;\n  items: SidebarItem[];\n}\n\nconst sidebarGroups: SidebarGroup[] = [\n  {\n    label: \"المعلومات الشخصية\",\n    items: [\n      {\n        label: \"الملف الشخصي\",\n        href: \"profile\",\n        icon: <User className=\"w-5 h-5\" />,\n      },\n      {\n        label: \"العنوان\",\n        href: \"address\",\n        icon: <MapPin className=\"w-5 h-5\" />,\n      },\n      {\n        label: \"كلمة المرور\",\n        href: \"password\",\n        icon: <Lock className=\"w-5 h-5\" />,\n      },\n    ],\n  },\n  {\n    label: \"المالية\",\n    items: [\n      {\n        label: \"إضافة رصيد\",\n        href: \"balance\",\n        icon: <CreditCard className=\"w-5 h-5\" />,\n      },\n      {\n        label: \"طلبات الرصيد\",\n        href: \"balance-requests\",\n        icon: <DollarSign className=\"w-5 h-5\" />,\n      },\n      {\n        label: \"المعاملات المالية\",\n        href: \"transactions\",\n        icon: <Receipt className=\"w-5 h-5\" />,\n      },\n    ],\n  },\n  {\n    label: \"المنتجات\",\n    items: [\n      {\n        label: \"طلب سعر\",\n        href: \"price-request\",\n        icon: <Package className=\"w-5 h-5\" />,\n      },\n      {\n        label: \"طلبات الأسعار\",\n        href: \"price-requests\",\n        icon: <FileText className=\"w-5 h-5\" />,\n      },\n    ],\n  },\n  {\n    label: \"الإدارة\",\n    items: [\n      {\n        label: \"لوحة الإدارة\",\n        href: \"admin\",\n        icon: <Users className=\"w-5 h-5\" />,\n        adminOnly: true,\n      },\n      {\n        label: \"الإعدادات\",\n        href: \"settings\",\n        icon: <Settings className=\"w-5 h-5\" />,\n      },\n    ],\n  },\n];\n\ninterface SidebarProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {\n  const [currentTab, setCurrentTab] = useState(activeTab);\n  const [openGroups, setOpenGroups] = useState<{ [key: string]: boolean }>({});\n\n  const handleClick = (href: string) => {\n    setCurrentTab(href);\n    onTabChange(href);\n  };\n\n  const toggleGroup = (groupLabel: string) => {\n    setOpenGroups((prev) => ({\n      ...prev,\n      [groupLabel]: !prev[groupLabel],\n    }));\n  };\n\n  useEffect(() => {\n    setCurrentTab(activeTab);\n    // Open the group that contains the active tab\n    sidebarGroups.forEach((group) => {\n      if (group.items.some((item) => item.href === activeTab)) {\n        setOpenGroups((prev) => ({\n          ...prev,\n          [group.label]: true,\n        }));\n      }\n    });\n  }, [activeTab]);\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Sidebar Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h2 className=\"text-lg font-bold text-white\">إعدادات الحساب</h2>\n        <p className=\"text-sm text-gray-300 mt-1\">إدارة ملفك الشخصي</p>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"p-4\">\n        <div className=\"space-y-4\">\n          {sidebarGroups.map((group) => {\n            const visibleItems = group.items.filter((item) => !item.adminOnly);\n\n            if (visibleItems.length === 0) return null;\n\n            return (\n              <div key={group.label} className=\"space-y-2\">\n                <button\n                  onClick={() => toggleGroup(group.label)}\n                  className=\"w-full flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n                >\n                  <span className=\"font-semibold\">{group.label}</span>\n                  {openGroups[group.label] ? (\n                    <ChevronUp className=\"w-4 h-4\" />\n                  ) : (\n                    <ChevronDown className=\"w-4 h-4\" />\n                  )}\n                </button>\n\n                {openGroups[group.label] && (\n                  <div className=\"space-y-1 mr-4\">\n                    {visibleItems.map((item) => (\n                      <button\n                        key={item.href}\n                        onClick={() => handleClick(item.href)}\n                        className={`w-full group flex items-center justify-between px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${\n                          currentTab === item.href\n                            ? \"bg-gradient-to-r from-gray-900 to-black text-white shadow-lg\"\n                            : \"text-gray-700 hover:text-gray-900 hover:bg-gray-50\"\n                        }`}\n                      >\n                        <div className=\"flex items-center\">\n                          <span\n                            className={`transition-colors duration-200 ml-3 ${\n                              currentTab === item.href\n                                ? \"text-white\"\n                                : \"text-gray-500 group-hover:text-gray-700\"\n                            }`}\n                          >\n                            {item.icon}\n                          </span>\n                          <span>{item.label}</span>\n                        </div>\n                        {currentTab === item.href && (\n                          <div className=\"w-2 h-2 bg-white rounded-full opacity-80\"></div>\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-100\">\n        <p className=\"text-xs text-gray-500 text-center\">\n          إدارة حسابك بسهولة وأمان\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAgCA,MAAM,gBAAgC;IACpC;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACxB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC1B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACxB;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC9B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC9B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3B;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC3B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC5B;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,WAAW;YACb;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC5B;SACD;IACH;CACD;AAOD,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAE1E,MAAM,cAAc,CAAC;QACnB,cAAc;QACd,YAAY;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW;YACjC,CAAC;IACH;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,8CAA8C;QAC9C,cAAc,OAAO,CAAC,CAAC;YACrB,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,YAAY;gBACvD,cAAc,CAAC,OAAS,CAAC;wBACvB,GAAG,IAAI;wBACP,CAAC,MAAM,KAAK,CAAC,EAAE;oBACjB,CAAC;YACH;QACF;IACF,GAAG;QAAC;KAAU;IAEd,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC;wBAClB,MAAM,eAAe,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,CAAC,KAAK,SAAS;wBAEjE,IAAI,aAAa,MAAM,KAAK,GAAG,OAAO;wBAEtC,qBACE,8OAAC;4BAAsB,WAAU;;8CAC/B,8OAAC;oCACC,SAAS,IAAM,YAAY,MAAM,KAAK;oCACtC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAiB,MAAM,KAAK;;;;;;wCAC3C,UAAU,CAAC,MAAM,KAAK,CAAC,iBACtB,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;gCAI1B,UAAU,CAAC,MAAM,KAAK,CAAC,kBACtB,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4CAEC,SAAS,IAAM,YAAY,KAAK,IAAI;4CACpC,WAAW,CAAC,oHAAoH,EAC9H,eAAe,KAAK,IAAI,GACpB,iEACA,sDACJ;;8DAEF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oCAAoC,EAC9C,eAAe,KAAK,IAAI,GACpB,eACA,2CACJ;sEAED,KAAK,IAAI;;;;;;sEAEZ,8OAAC;sEAAM,KAAK,KAAK;;;;;;;;;;;;gDAElB,eAAe,KAAK,IAAI,kBACvB,8OAAC;oDAAI,WAAU;;;;;;;2CArBZ,KAAK,IAAI;;;;;;;;;;;2BAjBd,MAAM,KAAK;;;;;oBA8CzB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAMzD;uCAEe", "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/common/Input.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\nimport { AlertCircle } from \"lucide-react\";\n\ninterface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  id: string;\n  label?: string;\n  error?: boolean;\n  errorMessage?: string;\n  icon?: React.ReactNode;\n}\n\nconst Input: React.FC<InputProps> = ({\n  id,\n  label,\n  error,\n  errorMessage,\n  icon,\n  className = \"\",\n  required,\n  ...props\n}) => {\n  return (\n    <div className=\"w-full\">\n      {label && (\n        <label\n          htmlFor={id}\n          className=\"block text-sm font-medium text-gray-700 mb-1 text-right\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 mr-1\">*</span>}\n        </label>\n      )}\n      <div className=\"relative\">\n        {icon && (\n          <div\n            className={`absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10 ${\n              props.disabled && \"text-gray-700 \"\n            }`}\n          >\n            {icon}\n          </div>\n        )}\n        {error && (\n          <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-red-500\">\n            <AlertCircle className=\"h-5 w-5\" />\n          </div>\n        )}\n        <input\n          id={id}\n          dir=\"rtl\"\n          className={`appearance-none rounded-lg relative block w-full px-3 py-3 ${\n            icon ? \"pr-10\" : \"pr-3\"\n          } ${error ? \"pl-10\" : \"pl-3\"} border ${\n            error ? \"border-red-500\" : \"border-gray-300\"\n          } placeholder-gray-500 text-gray-900 text-right focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent  ${\n            props.disabled && \"bg-gray-100 text-gray-500 cursor-not-allowed\"\n          } ${className}`}\n          {...props}\n          required={false}\n        />\n      </div>\n      {error && errorMessage && (\n        <p className=\"mt-1 text-sm text-red-500 !text-right\">{errorMessage}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AACA;;;AAUA,MAAM,QAA8B,CAAC,EACnC,EAAE,EACF,KAAK,EACL,KAAK,EACL,YAAY,EACZ,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBACC,WAAW,CAAC,2EAA2E,EACrF,MAAM,QAAQ,IAAI,kBAClB;kCAED;;;;;;oBAGJ,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAG3B,8OAAC;wBACC,IAAI;wBACJ,KAAI;wBACJ,WAAW,CAAC,2DAA2D,EACrE,OAAO,UAAU,OAClB,CAAC,EAAE,QAAQ,UAAU,OAAO,QAAQ,EACnC,QAAQ,mBAAmB,kBAC5B,0HAA0H,EACzH,MAAM,QAAQ,IAAI,+CACnB,CAAC,EAAE,WAAW;wBACd,GAAG,KAAK;wBACT,UAAU;;;;;;;;;;;;YAGb,SAAS,8BACR,8OAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAI9D;uCAEe", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/PhoneInput.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { ChevronDown } from \"lucide-react\";\nimport ReactCountryFlag from \"react-country-flag\";\nimport { CountryCode } from \"../utils/countryData\";\n\nexport interface PhoneInputProps {\n  value: string;\n  countryCode: string;\n  onChange: (value: string) => void;\n  onCountryChange: (code: string) => void;\n  error?: boolean;\n  errorMessage?: string;\n  countryCodes: CountryCode[];\n  isRTL?: boolean; // New prop for RTL support\n}\n\nconst PhoneInput: React.FC<PhoneInputProps> = ({\n  value,\n  countryCode,\n  onChange,\n  onCountryChange,\n  error,\n  errorMessage,\n  countryCodes,\n  isRTL = true, // default to Arabic/RTL\n}) => {\n  const [showCountryDropdown, setShowCountryDropdown] = useState(false);\n  const [countrySearchTerm, setCountrySearchTerm] = useState(\"\");\n  const phoneInputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node)\n      ) {\n        setShowCountryDropdown(false);\n        setCountrySearchTerm(\"\");\n      }\n    }\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className={`relative ${isRTL ? \"rtl\" : \"\"} w-full`}>\n      <div\n        className={`flex rounded-lg border ${\n          error ? \"border-red-500\" : \"border-gray-300\"\n        } focus-within:ring-2 focus-within:ring-black focus-within:border-transparent`}\n        dir={isRTL ? \"rtl\" : \"ltr\"}\n      >\n        <div\n          className={`flex items-center justify-center px-3 cursor-pointer bg-gray-50 ${\n            isRTL ? \"rounded-r-lg\" : \"rounded-l-lg\"\n          }`}\n          onClick={() => setShowCountryDropdown(!showCountryDropdown)}\n        >\n          <ReactCountryFlag\n            countryCode={countryCode}\n            svg\n            style={{ width: \"1.5em\", height: \"1.5em\" }}\n            className={`${isRTL ? \"ml-2\" : \"mr-2\"}`}\n          />\n          <ChevronDown className=\"h-5 w-5 text-gray-500\" />{\" \"}\n          {/* Increased arrow size */}\n        </div>\n        <input\n          ref={phoneInputRef}\n          type=\"tel\"\n          className=\"appearance-none relative block w-full px-3 py-3 border-0 placeholder-gray-500 text-gray-900 focus:outline-none rounded-lg\"\n          placeholder={isRTL ? \"رقم الهاتف\" : \"Phone number\"}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          onFocus={() => setShowCountryDropdown(false)}\n          required={false}\n          dir={isRTL ? \"rtl\" : \"ltr\"}\n        />\n      </div>\n      {error && errorMessage && (\n        <p className=\"mt-1 text-sm text-red-500 text-right\">{errorMessage}</p>\n      )}\n\n      {showCountryDropdown && (\n        <div\n          ref={dropdownRef}\n          className={`absolute z-50 mt-1 w-72 max-h-72 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg ${\n            isRTL ? \"right-0\" : \"left-0\"\n          }`}\n        >\n          <div className=\"p-2 sticky top-0 bg-white border-b\">\n            <input\n              type=\"text\"\n              className=\"w-full p-2 border border-gray-300 rounded\"\n              placeholder={isRTL ? \"ابحث عن دولة...\" : \"Search countries...\"}\n              onClick={(e) => e.stopPropagation()}\n              onChange={(e) =>\n                setCountrySearchTerm(e.target.value.toLowerCase())\n              }\n              autoFocus\n              dir={isRTL ? \"rtl\" : \"ltr\"}\n            />\n          </div>\n          <div className=\"py-1\">\n            {countryCodes\n              .filter(\n                (country) =>\n                  country.name.toLowerCase().includes(countrySearchTerm) ||\n                  country.dialCode.includes(countrySearchTerm) ||\n                  country.code.toLowerCase().includes(countrySearchTerm)\n              )\n              .map((country) => (\n                <div\n                  key={country.code}\n                  className=\"flex items-center px-3 py-1.5 hover:bg-gray-100 cursor-pointer text-sm\"\n                  onClick={() => {\n                    onCountryChange(country.code);\n                    setShowCountryDropdown(false);\n                    phoneInputRef.current?.focus();\n                  }}\n                >\n                  <ReactCountryFlag\n                    countryCode={country.code}\n                    svg\n                    style={{ width: \"1.2em\", height: \"1.2em\" }}\n                    className={`${isRTL ? \"ml-2\" : \"mr-2\"}`}\n                  />\n                  <span className=\"w-12 inline-block\">{country.dialCode}</span>\n                  <span className=\"truncate\">{country.name}</span>\n                </div>\n              ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PhoneInput;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAcA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,WAAW,EACX,QAAQ,EACR,eAAe,EACf,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,QAAQ,IAAI,EACb;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,uBAAuB;gBACvB,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,QAAQ,GAAG,OAAO,CAAC;;0BACrD,8OAAC;gBACC,WAAW,CAAC,uBAAuB,EACjC,QAAQ,mBAAmB,kBAC5B,4EAA4E,CAAC;gBAC9E,KAAK,QAAQ,QAAQ;;kCAErB,8OAAC;wBACC,WAAW,CAAC,gEAAgE,EAC1E,QAAQ,iBAAiB,gBACzB;wBACF,SAAS,IAAM,uBAAuB,CAAC;;0CAEvC,8OAAC,mLAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,GAAG;gCACH,OAAO;oCAAE,OAAO;oCAAS,QAAQ;gCAAQ;gCACzC,WAAW,GAAG,QAAQ,SAAS,QAAQ;;;;;;0CAEzC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAA2B;;;;;;;kCAGpD,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,WAAU;wBACV,aAAa,QAAQ,eAAe;wBACpC,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,uBAAuB;wBACtC,UAAU;wBACV,KAAK,QAAQ,QAAQ;;;;;;;;;;;;YAGxB,SAAS,8BACR,8OAAC;gBAAE,WAAU;0BAAwC;;;;;;YAGtD,qCACC,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAC,oGAAoG,EAC9G,QAAQ,YAAY,UACpB;;kCAEF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,aAAa,QAAQ,oBAAoB;4BACzC,SAAS,CAAC,IAAM,EAAE,eAAe;4BACjC,UAAU,CAAC,IACT,qBAAqB,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;4BAEjD,SAAS;4BACT,KAAK,QAAQ,QAAQ;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,aACE,MAAM,CACL,CAAC,UACC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,sBACpC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,sBAC1B,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAEvC,GAAG,CAAC,CAAC,wBACJ,8OAAC;gCAEC,WAAU;gCACV,SAAS;oCACP,gBAAgB,QAAQ,IAAI;oCAC5B,uBAAuB;oCACvB,cAAc,OAAO,EAAE;gCACzB;;kDAEA,8OAAC,mLAAA,CAAA,UAAgB;wCACf,aAAa,QAAQ,IAAI;wCACzB,GAAG;wCACH,OAAO;4CAAE,OAAO;4CAAS,QAAQ;wCAAQ;wCACzC,WAAW,GAAG,QAAQ,SAAS,QAAQ;;;;;;kDAEzC,8OAAC;wCAAK,WAAU;kDAAqB,QAAQ,QAAQ;;;;;;kDACrD,8OAAC;wCAAK,WAAU;kDAAY,QAAQ,IAAI;;;;;;;+BAfnC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;AAuBnC;uCAEe", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/formUtils.ts"], "sourcesContent": ["// Form validation utilities\n\nexport interface FormErrors {\n  [key: string]: string | undefined;\n  email?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  relation?: string;\n  otherRelation?: string;\n  idImage?: string;\n  children?: string;\n  address?: string;\n  country?: string;\n  governorate?: string;\n  city?: string;\n  general?: string;\n}\n\nexport type PasswordStrength = \"weak\" | \"medium\" | \"strong\" | \"\";\n\n// Function to check password strength\nexport const checkPasswordStrength = (password: string): PasswordStrength => {\n  if (!password) return \"\";\n\n  // Check for minimum length\n  if (password.length < 6) return \"weak\";\n\n  // Check for complexity\n  const hasUppercase = /[A-Z]/.test(password);\n  const hasLowercase = /[a-z]/.test(password);\n  const hasNumbers = /[0-9]/.test(password);\n  const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n\n  const complexity =\n    (hasUppercase ? 1 : 0) +\n    (hasLowercase ? 1 : 0) +\n    (hasNumbers ? 1 : 0) +\n    (hasSpecialChars ? 1 : 0);\n\n  if (complexity === 4 && password.length >= 8) return \"strong\";\n  if (complexity >= 2 && password.length >= 6) return \"medium\";\n  return \"weak\";\n};\n\n// Function to validate email format\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Function to validate form data\nexport interface FormData {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  relation: string;\n  otherRelation?: string;\n  address?: string;\n  country?: string;\n  governorate?: string;\n  city?: string;\n}\n\nexport const validateForm = (formData: FormData, validateOptionalFields: boolean = false): FormErrors => {\n  const newErrors: FormErrors = {};\n\n  // Email validation\n  if (!formData.email) {\n    newErrors.email = \"Email is required\";\n  } else if (!isValidEmail(formData.email)) {\n    newErrors.email = \"Please enter a valid email address\";\n  }\n\n  // Password validation\n  if (!formData.password) {\n    newErrors.password = \"Password is required\";\n  } else if (formData.password.length < 6) {\n    newErrors.password = \"Password must be at least 6 characters\";\n  } else {\n    const strength = checkPasswordStrength(formData.password);\n    if (strength === \"weak\") {\n      newErrors.password =\n        \"Password is too weak. Include uppercase, lowercase, and numbers\";\n    }\n  }\n\n  // First name validation\n  if (!formData.firstName) {\n    newErrors.firstName = \"First name is required\";\n  } else if (formData.firstName.length < 2) {\n    newErrors.firstName = \"First name is too short\";\n  }\n\n  // Last name validation\n  if (!formData.lastName) {\n    newErrors.lastName = \"Last name is required\";\n  } else if (formData.lastName.length < 2) {\n    newErrors.lastName = \"Last name is too short\";\n  }\n\n  // Relation validation\n  if (!formData.relation) {\n    newErrors.relation = \"Relationship is required\";\n  } else if (formData.relation === \"other\" && !formData.otherRelation) {\n    newErrors.relation = \"Please specify your relationship\";\n  } else if (formData.relation === \"other\" && formData.otherRelation && formData.otherRelation.length < 2) {\n    newErrors.relation = \"Relationship description is too short\";\n  }\n\n  // Phone validation (always validate if provided)\n  if (!formData.phone) {\n    newErrors.phone = \"Phone number is required\";\n  } else  {\n    // Remove any non-digit characters for validation\n    const phoneDigits = formData.phone.replace(/\\D/g, \"\");\n\n    // Simple validation that only checks the length of the phone number\n    // Most international phone numbers are between 8 and 15 digits\n    if (phoneDigits.length < 8 || phoneDigits.length > 15) {\n      newErrors.phone = \"Please enter a valid phone number (8-15 digits)\";\n    }\n  }\n  if (formData.phone) {\n\n  }\n\n  // Only validate optional fields if they are shown\n  if (validateOptionalFields) {\n    // Address validation (optional but validate if provided)\n    if (formData.address && formData.address.length < 5) {\n      newErrors.address = \"Address is too short\";\n    }\n\n    // Country validation (required if address is provided)\n    if (formData.address && !formData.country) {\n      newErrors.country = \"Country is required when address is provided\";\n    }\n\n    // Governorate validation (required if address is provided and country is Egypt)\n    if (formData.address && formData.country === \"Egypt\" && !formData.governorate) {\n      newErrors.governorate = \"Governorate is required when address is provided\";\n    }\n\n    // City validation (required if address is provided)\n    if (formData.address && !formData.city) {\n      newErrors.city = \"City is required when address is provided\";\n    }\n  }\n\n  return newErrors;\n};\n\n// Country code interface\nexport interface CountryCode {\n  code: string;\n  dialCode: string;\n  name: string;\n}\n\n// Format phone number with country code\nexport const formatPhoneWithCountryCode = (\n  phone: string,\n  countryCode: string,\n  countryCodes: Record<string, CountryCode>\n): string => {\n  if (!phone) return '';\n\n  // Get the selected country's dial code\n  const selectedCountry = countryCodes[countryCode];\n  if (!selectedCountry) return phone;\n\n  // Format the phone number with the country code\n  return phone ?\n    `${selectedCountry?.dialCode}${phone.startsWith('0') ? phone.substring(1) : phone}` :\n    '';\n};\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;AAuBrB,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,UAAU,OAAO;IAEtB,2BAA2B;IAC3B,IAAI,SAAS,MAAM,GAAG,GAAG,OAAO;IAEhC,uBAAuB;IACvB,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,QAAQ,IAAI,CAAC;IAChC,MAAM,kBAAkB,yBAAyB,IAAI,CAAC;IAEtD,MAAM,aACJ,CAAC,eAAe,IAAI,CAAC,IACrB,CAAC,eAAe,IAAI,CAAC,IACrB,CAAC,aAAa,IAAI,CAAC,IACnB,CAAC,kBAAkB,IAAI,CAAC;IAE1B,IAAI,eAAe,KAAK,SAAS,MAAM,IAAI,GAAG,OAAO;IACrD,IAAI,cAAc,KAAK,SAAS,MAAM,IAAI,GAAG,OAAO;IACpD,OAAO;AACT;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAiBO,MAAM,eAAe,CAAC,UAAoB,yBAAkC,KAAK;IACtF,MAAM,YAAwB,CAAC;IAE/B,mBAAmB;IACnB,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAO,IAAI,CAAC,aAAa,SAAS,KAAK,GAAG;QACxC,UAAU,KAAK,GAAG;IACpB;IAEA,sBAAsB;IACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB,OAAO;QACL,MAAM,WAAW,sBAAsB,SAAS,QAAQ;QACxD,IAAI,aAAa,QAAQ;YACvB,UAAU,QAAQ,GAChB;QACJ;IACF;IAEA,wBAAwB;IACxB,IAAI,CAAC,SAAS,SAAS,EAAE;QACvB,UAAU,SAAS,GAAG;IACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;QACxC,UAAU,SAAS,GAAG;IACxB;IAEA,uBAAuB;IACvB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB;IAEA,sBAAsB;IACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW,CAAC,SAAS,aAAa,EAAE;QACnE,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,KAAK,WAAW,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;QACvG,UAAU,QAAQ,GAAG;IACvB;IAEA,iDAAiD;IACjD,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAQ;QACN,iDAAiD;QACjD,MAAM,cAAc,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO;QAElD,oEAAoE;QACpE,+DAA+D;QAC/D,IAAI,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,IAAI;YACrD,UAAU,KAAK,GAAG;QACpB;IACF;IACA,IAAI,SAAS,KAAK,EAAE,CAEpB;IAEA,kDAAkD;IAClD,IAAI,wBAAwB;QAC1B,yDAAyD;QACzD,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACnD,UAAU,OAAO,GAAG;QACtB;QAEA,uDAAuD;QACvD,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YACzC,UAAU,OAAO,GAAG;QACtB;QAEA,gFAAgF;QAChF,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC7E,UAAU,WAAW,GAAG;QAC1B;QAEA,oDAAoD;QACpD,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACtC,UAAU,IAAI,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AAUO,MAAM,6BAA6B,CACxC,OACA,aACA;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,uCAAuC;IACvC,MAAM,kBAAkB,YAAY,CAAC,YAAY;IACjD,IAAI,CAAC,iBAAiB,OAAO;IAE7B,gDAAgD;IAChD,OAAO,QACL,GAAG,iBAAiB,WAAW,MAAM,UAAU,CAAC,OAAO,MAAM,SAAS,CAAC,KAAK,OAAO,GACnF;AACJ", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/countryData.ts"], "sourcesContent": ["// Country data for dropdowns and phone inputs\n\nexport interface CountryCode {\n  code: string;\n  name: string;\n  dialCode: string;\n  flag: string;\n}\n\n// List of country codes (Arab and European countries first, then others)\nexport const countryCodes: CountryCode[] = [\n  // Arab Countries\n  { code: \"EG\", name: \"Egypt\", dialCode: \"+20\", flag: \"🇪🇬\" },\n  { code: \"SA\", name: \"Saudi Arabia\", dialCode: \"+966\", flag: \"🇸🇦\" },\n  { code: \"AE\", name: \"United Arab Emirates\", dialCode: \"+971\", flag: \"🇦🇪\" },\n  { code: \"KW\", name: \"Kuwait\", dialCode: \"+965\", flag: \"🇰🇼\" },\n  { code: \"QA\", name: \"Qatar\", dialCode: \"+974\", flag: \"🇶🇦\" },\n  { code: \"OM\", name: \"Oman\", dialCode: \"+968\", flag: \"🇴🇲\" },\n  { code: \"BH\", name: \"Bahrain\", dialCode: \"+973\", flag: \"🇧🇭\" },\n  { code: \"<PERSON><PERSON>\", name: \"Jordan\", dialCode: \"+962\", flag: \"🇯🇴\" },\n  { code: \"LB\", name: \"Lebanon\", dialCode: \"+961\", flag: \"🇱🇧\" },\n  { code: \"SY\", name: \"Syria\", dialCode: \"+963\", flag: \"🇸🇾\" },\n  { code: \"IQ\", name: \"Iraq\", dialCode: \"+964\", flag: \"🇮🇶\" },\n  { code: \"PS\", name: \"Palestine\", dialCode: \"+970\", flag: \"🇵🇸\" },\n  { code: \"YE\", name: \"Yemen\", dialCode: \"+967\", flag: \"🇾🇪\" },\n  { code: \"DZ\", name: \"Algeria\", dialCode: \"+213\", flag: \"🇩🇿\" },\n  { code: \"MA\", name: \"Morocco\", dialCode: \"+212\", flag: \"🇲🇦\" },\n  { code: \"TN\", name: \"Tunisia\", dialCode: \"+216\", flag: \"🇹🇳\" },\n  { code: \"LY\", name: \"Libya\", dialCode: \"+218\", flag: \"🇱🇾\" },\n  { code: \"SD\", name: \"Sudan\", dialCode: \"+249\", flag: \"🇸🇩\" },\n  { code: \"MR\", name: \"Mauritania\", dialCode: \"+222\", flag: \"🇲🇷\" },\n  { code: \"SO\", name: \"Somalia\", dialCode: \"+252\", flag: \"🇸🇴\" },\n  { code: \"DJ\", name: \"Djibouti\", dialCode: \"+253\", flag: \"🇩🇯\" },\n  { code: \"KM\", name: \"Comoros\", dialCode: \"+269\", flag: \"🇰🇲\" },\n\n  // European Countries\n  { code: \"GB\", name: \"United Kingdom\", dialCode: \"+44\", flag: \"🇬🇧\" },\n  { code: \"DE\", name: \"Germany\", dialCode: \"+49\", flag: \"🇩🇪\" },\n  { code: \"FR\", name: \"France\", dialCode: \"+33\", flag: \"🇫🇷\" },\n  { code: \"IT\", name: \"Italy\", dialCode: \"+39\", flag: \"🇮🇹\" },\n  { code: \"ES\", name: \"Spain\", dialCode: \"+34\", flag: \"🇪🇸\" },\n  { code: \"RU\", name: \"Russia\", dialCode: \"+7\", flag: \"🇷🇺\" },\n  { code: \"UA\", name: \"Ukraine\", dialCode: \"+380\", flag: \"🇺🇦\" },\n  { code: \"PL\", name: \"Poland\", dialCode: \"+48\", flag: \"🇵🇱\" },\n  { code: \"RO\", name: \"Romania\", dialCode: \"+40\", flag: \"🇷🇴\" },\n  { code: \"NL\", name: \"Netherlands\", dialCode: \"+31\", flag: \"🇳🇱\" },\n  { code: \"BE\", name: \"Belgium\", dialCode: \"+32\", flag: \"🇧🇪\" },\n  { code: \"SE\", name: \"Sweden\", dialCode: \"+46\", flag: \"🇸🇪\" },\n  { code: \"NO\", name: \"Norway\", dialCode: \"+47\", flag: \"🇳🇴\" },\n  { code: \"DK\", name: \"Denmark\", dialCode: \"+45\", flag: \"🇩🇰\" },\n  { code: \"FI\", name: \"Finland\", dialCode: \"+358\", flag: \"🇫🇮\" },\n  { code: \"CH\", name: \"Switzerland\", dialCode: \"+41\", flag: \"🇨🇭\" },\n  { code: \"AT\", name: \"Austria\", dialCode: \"+43\", flag: \"🇦🇹\" },\n  { code: \"PT\", name: \"Portugal\", dialCode: \"+351\", flag: \"🇵🇹\" },\n  { code: \"GR\", name: \"Greece\", dialCode: \"+30\", flag: \"🇬🇷\" },\n  { code: \"TR\", name: \"Turkey\", dialCode: \"+90\", flag: \"🇹🇷\" },\n  { code: \"HU\", name: \"Hungary\", dialCode: \"+36\", flag: \"🇭🇺\" },\n  { code: \"CZ\", name: \"Czech Republic\", dialCode: \"+420\", flag: \"🇨🇿\" },\n  { code: \"IE\", name: \"Ireland\", dialCode: \"+353\", flag: \"🇮🇪\" },\n  { code: \"BG\", name: \"Bulgaria\", dialCode: \"+359\", flag: \"🇧🇬\" },\n  { code: \"HR\", name: \"Croatia\", dialCode: \"+385\", flag: \"🇭🇷\" },\n  { code: \"LT\", name: \"Lithuania\", dialCode: \"+370\", flag: \"🇱🇹\" },\n  { code: \"SK\", name: \"Slovakia\", dialCode: \"+421\", flag: \"🇸🇰\" },\n  { code: \"LV\", name: \"Latvia\", dialCode: \"+371\", flag: \"🇱🇻\" },\n  { code: \"SI\", name: \"Slovenia\", dialCode: \"+386\", flag: \"🇸🇮\" },\n  { code: \"EE\", name: \"Estonia\", dialCode: \"+372\", flag: \"🇪🇪\" },\n  { code: \"CY\", name: \"Cyprus\", dialCode: \"+357\", flag: \"🇨🇾\" },\n  { code: \"LU\", name: \"Luxembourg\", dialCode: \"+352\", flag: \"🇱🇺\" },\n  { code: \"MT\", name: \"Malta\", dialCode: \"+356\", flag: \"🇲🇹\" },\n  { code: \"IS\", name: \"Iceland\", dialCode: \"+354\", flag: \"🇮🇸\" },\n  { code: \"AL\", name: \"Albania\", dialCode: \"+355\", flag: \"🇦🇱\" },\n  { code: \"MD\", name: \"Moldova\", dialCode: \"+373\", flag: \"🇲🇩\" },\n  { code: \"MK\", name: \"North Macedonia\", dialCode: \"+389\", flag: \"🇲🇰\" },\n  { code: \"ME\", name: \"Montenegro\", dialCode: \"+382\", flag: \"🇲🇪\" },\n  { code: \"RS\", name: \"Serbia\", dialCode: \"+381\", flag: \"🇷🇸\" },\n  { code: \"BA\", name: \"Bosnia and Herzegovina\", dialCode: \"+387\", flag: \"🇧🇦\" },\n  { code: \"MC\", name: \"Monaco\", dialCode: \"+377\", flag: \"🇲🇨\" },\n  { code: \"LI\", name: \"Liechtenstein\", dialCode: \"+423\", flag: \"🇱🇮\" },\n  { code: \"SM\", name: \"San Marino\", dialCode: \"+378\", flag: \"🇸🇲\" },\n  { code: \"VA\", name: \"Vatican City\", dialCode: \"+379\", flag: \"🇻🇦\" },\n  { code: \"AD\", name: \"Andorra\", dialCode: \"+376\", flag: \"🇦🇩\" },\n\n  // Other major countries\n  { code: \"US\", name: \"United States\", dialCode: \"+1\", flag: \"🇺🇸\" },\n  { code: \"CA\", name: \"Canada\", dialCode: \"+1\", flag: \"🇨🇦\" },\n  { code: \"AU\", name: \"Australia\", dialCode: \"+61\", flag: \"🇦🇺\" },\n  { code: \"IN\", name: \"India\", dialCode: \"+91\", flag: \"🇮🇳\" },\n  { code: \"CN\", name: \"China\", dialCode: \"+86\", flag: \"🇨🇳\" },\n  { code: \"JP\", name: \"Japan\", dialCode: \"+81\", flag: \"🇯🇵\" },\n  { code: \"BR\", name: \"Brazil\", dialCode: \"+55\", flag: \"🇧🇷\" },\n];\n\n// List of Egyptian governorates\nexport const egyptianGovernorates = [\n  \"Cairo\",\n  \"Alexandria\",\n  \"Giza\",\n  \"Qalyubia\",\n  \"Sharqia\",\n  \"Gharbia\",\n  \"Menoufia\",\n  \"Beheira\",\n  \"Kafr El Sheikh\",\n  \"Damietta\",\n  \"Port Said\",\n  \"Ismailia\",\n  \"Suez\",\n  \"North Sinai\",\n  \"South Sinai\",\n  \"Fayoum\",\n  \"Beni Suef\",\n  \"Minya\",\n  \"Assiut\",\n  \"Sohag\",\n  \"Qena\",\n  \"Luxor\",\n  \"Aswan\",\n  \"Red Sea\",\n  \"New Valley\",\n  \"Matrouh\",\n];\n\n// List of Egyptian cities for the city dropdown\nexport const egyptianCities = [\n  \"Cairo\",\n  \"Alexandria\",\n  \"Giza\",\n  \"Shubra El Kheima\",\n  \"Port Said\",\n  \"Suez\",\n  \"Luxor\",\n  \"Aswan\",\n  \"Asyut\",\n  \"Ismailia\",\n  \"Faiyum\",\n  \"Zagazig\",\n  \"Damietta\",\n  \"Mansoura\",\n  \"Tanta\",\n  \"Hurghada\",\n  \"Sohag\",\n  \"Shibin El Kom\",\n  \"Beni Suef\",\n  \"Qena\",\n  \"Minya\",\n  \"Damanhur\",\n  \"El Mahalla El Kubra\",\n  \"Kafr El Sheikh\",\n  \"Arish\",\n  \"Mallawi\",\n  \"Banha\",\n  \"Belbeis\",\n  \"Marsa Matruh\",\n  \"Idfu\",\n  \"Mit Ghamr\",\n  \"Al-Hamidiyya\",\n  \"Desouk\",\n  \"Qalyub\",\n  \"Abu Kabir\",\n  \"Kafr El Dawwar\",\n  \"Girga\",\n  \"Akhmim\",\n  \"El Tor\",\n  \"Dahab\",\n  \"Sharm El Sheikh\",\n  \"New Cairo\",\n  \"6th of October City\",\n  \"10th of Ramadan City\",\n  \"El Obour\",\n  \"El Shorouk\",\n  \"New Borg El Arab\",\n];\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAUvC,MAAM,eAA8B;IACzC,iBAAiB;IACjB;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAgB,UAAU;QAAQ,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAwB,UAAU;QAAQ,MAAM;IAAO;IAC3E;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAQ,UAAU;QAAQ,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAQ,UAAU;QAAQ,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAQ,MAAM;IAAO;IAChE;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAE9D,qBAAqB;IACrB;QAAE,MAAM;QAAM,MAAM;QAAkB,UAAU;QAAO,MAAM;IAAO;IACpE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAM,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAe,UAAU;QAAO,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAe,UAAU;QAAO,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAO,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAkB,UAAU;QAAQ,MAAM;IAAO;IACrE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAQ,MAAM;IAAO;IAChE;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;QAAQ,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAQ,MAAM;IAAO;IAC5D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAC9D;QAAE,MAAM;QAAM,MAAM;QAAmB,UAAU;QAAQ,MAAM;IAAO;IACtE;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAA0B,UAAU;QAAQ,MAAM;IAAO;IAC7E;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAQ,MAAM;IAAO;IAC7D;QAAE,MAAM;QAAM,MAAM;QAAiB,UAAU;QAAQ,MAAM;IAAO;IACpE;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;QAAQ,MAAM;IAAO;IACjE;QAAE,MAAM;QAAM,MAAM;QAAgB,UAAU;QAAQ,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;QAAQ,MAAM;IAAO;IAE9D,wBAAwB;IACxB;QAAE,MAAM;QAAM,MAAM;QAAiB,UAAU;QAAM,MAAM;IAAO;IAClE;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAM,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAa,UAAU;QAAO,MAAM;IAAO;IAC/D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;QAAO,MAAM;IAAO;IAC3D;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;QAAO,MAAM;IAAO;CAC7D;AAGM,MAAM,uBAAuB;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 1687, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/accountUtils.ts"], "sourcesContent": ["import { FormErrors, PasswordStrength, checkPasswordStrength } from \"./formUtils\";\nimport { countryCodes } from \"./countryData\";\nimport { ProfileFormData, User, ExtendedUserType } from \"../types/account\";\n\nexport const extractPhoneNumber = (\n  phoneWithCode: string\n): { phoneNumber: string; countryCode: string } => {\n  let phoneNumber = \"\";\n  let countryCode = \"EG\";\n\n  if (!phoneWithCode) return { phoneNumber, countryCode };\n\n  console.log(\"Extracting from phone:\", phoneWithCode);\n\n  // Try to find the country code from the phone number\n  for (const country of countryCodes) {\n    if (phoneWithCode.startsWith(country.dialCode)) {\n      countryCode = country.code;\n      phoneNumber = phoneWithCode.substring(country.dialCode.length);\n      console.log(`Found country ${country.name} (${country.code}) with dial code ${country.dialCode}, remaining phone: ${phoneNumber}`);\n      return { phoneNumber, countryCode };\n    }\n  }\n\n  // If we couldn't find a matching country code, just return the whole number\n  console.log(\"No matching country code found, using default:\", countryCode);\n  return { phoneNumber: phoneWithCode, countryCode };\n};\n\nexport const getCountryCodeFromName = (countryName: string): string => {\n  if (!countryName) return \"EG\";\n\n  const country = countryCodes.find(\n    (c) => c.name.toLowerCase() === countryName.toLowerCase()\n  );\n\n  return country ? country.code : \"EG\";\n};\n\nexport const validateProfileForm = (\n  formData: ProfileFormData,\n  validateOptionalFields: boolean = false\n): { [key: string]: string } => {\n  const newErrors: { [key: string]: string } = {};\n\n  if (!formData.email) {\n    newErrors.email = \"البريد الإلكتروني مطلوب\";\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n    newErrors.email = \"يرجى إدخال بريد إلكتروني صحيح\";\n  }\n\n  if (!formData.firstName) {\n    newErrors.firstName = \"الاسم الأول مطلوب\";\n  } else if (formData.firstName.length < 2) {\n    newErrors.firstName = \"الاسم الأول قصير جدًا\";\n  }\n\n  if (!formData.lastName) {\n    newErrors.lastName = \"اسم العائلة مطلوب\";\n  } else if (formData.lastName.length < 2) {\n    newErrors.lastName = \"اسم العائلة قصير جدًا\";\n  }\n\n  // التحقق من رقم الهاتف (مطلوب)\n  if (!formData.phone) {\n    errors.phone = 'رقم الهاتف مطلوب';\n  } else if (!/^\\d+$/.test(formData.phone)) {\n    newErrors.phone = 'رقم الهاتف يجب أن يحتوي على أرقام فقط';\n  } else if (formData.phone.length < 7) {\n    newErrors.phone = 'رقم الهاتف يجب أن يكون 7 أرقام على الأقل';\n  }\n\n  \n\n  \n\n  if (validateOptionalFields) {\n    if (formData.address && formData.address.length < 5) {\n      newErrors.address = \"العنوان قصير جدًا\";\n    }\n\n    if (formData.address && !formData.country) {\n      newErrors.country = \"الدولة مطلوبة عند إدخال العنوان\";\n    }\n\n    if (formData.address && formData.country === \"Egypt\" && !formData.governorate) {\n      newErrors.governorate = \"المحافظة مطلوبة عند إدخال العنوان\";\n    }\n\n    if (formData.address && !formData.city) {\n      newErrors.city = \"المدينة مطلوبة عند إدخال العنوان\";\n    }\n  }\n\n  return newErrors;\n};\n\nexport const validatePasswordForm = (\n  currentPassword: string,\n  newPassword: string,\n  confirmPassword: string\n): FormErrors => {\n  const newErrors: FormErrors = {};\n\n  if (!currentPassword) {\n    newErrors.password = \"كلمة المرور الحالية مطلوبة\";\n  }\n\n  if (!newPassword) {\n    newErrors.password = \"كلمة المرور الجديدة مطلوبة\";\n  } else if (newPassword.length < 6) {\n    newErrors.password = \"يجب أن تكون كلمة المرور 6 أحرف على الأقل\";\n  } else {\n    const strength = checkPasswordStrength(newPassword);\n    if (strength === \"weak\") {\n      newErrors.password = \"كلمة المرور ضعيفة جدًا. يجب أن تحتوي على حروف كبيرة وصغيرة وأرقام\";\n    }\n  }\n\n  if (!confirmPassword) {\n    newErrors.password = \"يرجى تأكيد كلمة المرور\";\n  } else if (newPassword !== confirmPassword) {\n    newErrors.password = \"كلمتا المرور الجديدتان غير متطابقتين\";\n  }\n  console.log(newErrors);\n  return newErrors;\n};\n\nexport const hasProfileFormChanged = (\n  profileForm: ProfileFormData,\n  user: ExtendedUserType | null,\n  showOptionalFields: boolean\n): boolean => {\n  if (!user) return false;\n\n  if (\n    profileForm.firstName !== user.first_name ||\n    profileForm.lastName !== user.last_name ||\n    profileForm.email !== user.email\n  ) {\n    return true;\n  }\n\n  if (showOptionalFields) {\n    // تنسيق رقم الهاتف مع رمز الدولة\n    const selectedCountry = countryCodes.find(c => c.code === profileForm.phoneCountry);\n    const dialCode = selectedCountry?.dialCode || \"\";\n    const phoneNumber = profileForm.phone || \"\";\n    const formattedPhone = phoneNumber\n      ? `${dialCode}${phoneNumber.startsWith('0') ? phoneNumber.substring(1) : phoneNumber}`\n      : \"\";\n\n    const userPhone = (user as ExtendedUserType).phone_number || user.phone || \"\";\n\n    if (\n      formattedPhone !== userPhone ||\n      profileForm.address !== (user.address || \"\") ||\n      profileForm.country !== (user.country || \"Egypt\") ||\n      profileForm.city !== (user.city || \"\") ||\n      profileForm.governorate !== (user.governorate || \"\") ||\n      profileForm.countryCode !==\n        (user.country_code || getCountryCodeFromName(user.country || \"Egypt\"))\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAGO,MAAM,qBAAqB,CAChC;IAEA,IAAI,cAAc;IAClB,IAAI,cAAc;IAElB,IAAI,CAAC,eAAe,OAAO;QAAE;QAAa;IAAY;IAEtD,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,qDAAqD;IACrD,KAAK,MAAM,WAAW,2HAAA,CAAA,eAAY,CAAE;QAClC,IAAI,cAAc,UAAU,CAAC,QAAQ,QAAQ,GAAG;YAC9C,cAAc,QAAQ,IAAI;YAC1B,cAAc,cAAc,SAAS,CAAC,QAAQ,QAAQ,CAAC,MAAM;YAC7D,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,iBAAiB,EAAE,QAAQ,QAAQ,CAAC,mBAAmB,EAAE,aAAa;YACjI,OAAO;gBAAE;gBAAa;YAAY;QACpC;IACF;IAEA,4EAA4E;IAC5E,QAAQ,GAAG,CAAC,kDAAkD;IAC9D,OAAO;QAAE,aAAa;QAAe;IAAY;AACnD;AAEO,MAAM,yBAAyB,CAAC;IACrC,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,UAAU,2HAAA,CAAA,eAAY,CAAC,IAAI,CAC/B,CAAC,IAAM,EAAE,IAAI,CAAC,WAAW,OAAO,YAAY,WAAW;IAGzD,OAAO,UAAU,QAAQ,IAAI,GAAG;AAClC;AAEO,MAAM,sBAAsB,CACjC,UACA,yBAAkC,KAAK;IAEvC,MAAM,YAAuC,CAAC;IAE9C,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,UAAU,KAAK,GAAG;IACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;QAC7D,UAAU,KAAK,GAAG;IACpB;IAEA,IAAI,CAAC,SAAS,SAAS,EAAE;QACvB,UAAU,SAAS,GAAG;IACxB,OAAO,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;QACxC,UAAU,SAAS,GAAG;IACxB;IAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;QACvC,UAAU,QAAQ,GAAG;IACvB;IAEA,+BAA+B;IAC/B,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,OAAO,KAAK,GAAG;IACjB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,KAAK,GAAG;QACxC,UAAU,KAAK,GAAG;IACpB,OAAO,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG;QACpC,UAAU,KAAK,GAAG;IACpB;IAMA,IAAI,wBAAwB;QAC1B,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACnD,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YACzC,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,KAAK,WAAW,CAAC,SAAS,WAAW,EAAE;YAC7E,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACtC,UAAU,IAAI,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AAEO,MAAM,uBAAuB,CAClC,iBACA,aACA;IAEA,MAAM,YAAwB,CAAC;IAE/B,IAAI,CAAC,iBAAiB;QACpB,UAAU,QAAQ,GAAG;IACvB;IAEA,IAAI,CAAC,aAAa;QAChB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,YAAY,MAAM,GAAG,GAAG;QACjC,UAAU,QAAQ,GAAG;IACvB,OAAO;QACL,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE;QACvC,IAAI,aAAa,QAAQ;YACvB,UAAU,QAAQ,GAAG;QACvB;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,UAAU,QAAQ,GAAG;IACvB,OAAO,IAAI,gBAAgB,iBAAiB;QAC1C,UAAU,QAAQ,GAAG;IACvB;IACA,QAAQ,GAAG,CAAC;IACZ,OAAO;AACT;AAEO,MAAM,wBAAwB,CACnC,aACA,MACA;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,IACE,YAAY,SAAS,KAAK,KAAK,UAAU,IACzC,YAAY,QAAQ,KAAK,KAAK,SAAS,IACvC,YAAY,KAAK,KAAK,KAAK,KAAK,EAChC;QACA,OAAO;IACT;IAEA,IAAI,oBAAoB;QACtB,iCAAiC;QACjC,MAAM,kBAAkB,2HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,YAAY;QAClF,MAAM,WAAW,iBAAiB,YAAY;QAC9C,MAAM,cAAc,YAAY,KAAK,IAAI;QACzC,MAAM,iBAAiB,cACnB,GAAG,WAAW,YAAY,UAAU,CAAC,OAAO,YAAY,SAAS,CAAC,KAAK,aAAa,GACpF;QAEJ,MAAM,YAAY,AAAC,KAA0B,YAAY,IAAI,KAAK,KAAK,IAAI;QAE3E,IACE,mBAAmB,aACnB,YAAY,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,EAAE,KAC3C,YAAY,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,OAAO,KAChD,YAAY,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE,KACrC,YAAY,WAAW,KAAK,CAAC,KAAK,WAAW,IAAI,EAAE,KACnD,YAAY,WAAW,KACrB,CAAC,KAAK,YAAY,IAAI,uBAAuB,KAAK,OAAO,IAAI,QAAQ,GACvE;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/navigationUtils.ts"], "sourcesContent": ["/**\n * Navigation utilities for handling redirects and storing temporary data\n */\n\n/**\n * Redirects the user to the email verification page and stores their email\n * @param email The user's email address\n * @param message Optional message to display on the verification page\n */\nexport const redirectToEmailVerification = (email: string, message?: string) => {\n  // Store the email in localStorage so it can be accessed on the verification page\n  if (email) {\n    localStorage.setItem('pendingActivationEmail', email);\n  }\n\n  // Store the message if provided\n  if (message) {\n    localStorage.setItem('emailVerificationMessage', message);\n  }\n\n  // Navigate to the activation page\n  window.location.href = '/activate-account';\n};\n\n/**\n * Triggers an email verification error popup\n * @param error The error object containing email verification data\n */\nexport const triggerEmailVerificationPopup = (error: any) => {\n  // Dispatch a custom event that the EmailVerificationHandler will listen for\n  const event = new CustomEvent('emailVerificationError', {\n    detail: error\n  });\n  window.dispatchEvent(event);\n};\n\n/**\n * Checks if the current API error is related to email verification\n * @param error The API error object\n * @returns True if the error is related to email verification\n */\nexport const isEmailVerificationError = (error: any): boolean => {\n  if (!error ) {\n    return false;\n  }\n\n  const data = error;\n\n  return (\n    data.email_not_verified !== undefined ||\n    (data.email_verified !== undefined && data.email_verified === \"False\") ||\n    (data.code === \"email_not_verified\") ||\n    (data.detail && data.detail.includes(\"email\") && data.detail.includes(\"verified\"))\n  );\n};\n\n/**\n * Extracts the email and message from an email verification error\n * @param error The API error object\n * @returns An object containing the email and message\n */\nexport const extractEmailVerificationData = (error: any): { email?: string; message?: string } => {\n  if (!error ) {\n    return {};\n  }\n\n  const data = error;\n\n  return {\n    email: data.email || '',\n    message: data.message || data.detail || data.email_not_verified || 'Your email address has not been verified. Please verify your email to continue.'\n  };\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;AACM,MAAM,8BAA8B,CAAC,OAAe;IACzD,iFAAiF;IACjF,IAAI,OAAO;QACT,aAAa,OAAO,CAAC,0BAA0B;IACjD;IAEA,gCAAgC;IAChC,IAAI,SAAS;QACX,aAAa,OAAO,CAAC,4BAA4B;IACnD;IAEA,kCAAkC;IAClC,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,4EAA4E;IAC5E,MAAM,QAAQ,IAAI,YAAY,0BAA0B;QACtD,QAAQ;IACV;IACA,OAAO,aAAa,CAAC;AACvB;AAOO,MAAM,2BAA2B,CAAC;IACvC,IAAI,CAAC,OAAQ;QACX,OAAO;IACT;IAEA,MAAM,OAAO;IAEb,OACE,KAAK,kBAAkB,KAAK,aAC3B,KAAK,cAAc,KAAK,aAAa,KAAK,cAAc,KAAK,WAC7D,KAAK,IAAI,KAAK,wBACd,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,YAAY,KAAK,MAAM,CAAC,QAAQ,CAAC;AAE1E;AAOO,MAAM,+BAA+B,CAAC;IAC3C,IAAI,CAAC,OAAQ;QACX,OAAO,CAAC;IACV;IAEA,MAAM,OAAO;IAEb,OAAO;QACL,OAAO,KAAK,KAAK,IAAI;QACrB,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,kBAAkB,IAAI;IACrE;AACF", "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/ProfileForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Loader2, User as UserIcon } from \"lucide-react\";\n\nimport Input from \"@/components/common/Input\";\nimport PhoneInput from \"@/components/PhoneInput\";\n\nimport { ProfileFormData, FormErrors, User } from \"@/types/account\";\nimport { ChildData } from \"@/types/auth\";\nimport { validateProfileForm } from \"@/utils/accountUtils\";\nimport { countryCodes } from \"@/utils/countryData\";\n\nimport { updateUserProfile } from \"@/services/authService\";\nimport {\n  isEmailVerificationError,\n  triggerEmailVerificationPopup,\n  redirectToEmailVerification,\n} from \"@/utils/navigationUtils\";\n\n// Extended User interface to handle both phone and phone_number fields\ninterface ExtendedUser extends Omit<User, \"children\"> {\n  phone: string;\n  phone_number?: string;\n  children: ChildData[];\n  governorate?: string;\n  city?: string;\n  other_relation?: string;\n}\n\ninterface ProfileFormProps {\n  initialFormData: ProfileFormData;\n  user: ExtendedUser | null;\n  onUserUpdate: (user: ExtendedUser) => void;\n}\n\nconst ProfileForm: React.FC<ProfileFormProps> = ({\n  initialFormData,\n  user,\n  onUserUpdate,\n}) => {\n  const [localFormData, setLocalFormData] =\n    useState<ProfileFormData>(initialFormData);\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [previewUrl, setPreviewUrl] = useState<string>(\"\");\n\n  const [updateLoading, setUpdateLoading] = useState(false);\n\n  // ضبط رابط المعاينة من صورة الهوية الموجودة\n  useEffect(() => {\n    if (initialFormData.idImage) {\n      if (typeof initialFormData.idImage === \"string\") {\n        setPreviewUrl(initialFormData.idImage);\n      } else if (initialFormData.idImage instanceof File) {\n        const url = URL.createObjectURL(initialFormData.idImage);\n        setPreviewUrl(url);\n      }\n    } else {\n      setPreviewUrl(\"\");\n    }\n  }, [initialFormData.idImage]);\n\n  // تنظيف رابط المعاينة عند إلغاء تحميل المكون\n  useEffect(() => {\n    return () => {\n      if (previewUrl && !previewUrl.startsWith(\"http\")) {\n        URL.revokeObjectURL(previewUrl);\n      }\n    };\n  }, [previewUrl]);\n\n  const handlePhoneChange = (value: string) => {\n    console.log(\"تم تغيير رقم الهاتف:\", value);\n    setLocalFormData((prev) => {\n      const updated = { ...prev, phone: value };\n      console.log(\"تم تحديث بيانات النموذج (الهاتف):\", updated);\n      return updated;\n    });\n  };\n\n  const handlePhoneCountryChange = (code: string) => {\n    console.log(\"تم تغيير رمز الدولة للهاتف:\", code);\n    setLocalFormData((prev) => {\n      const updated = { ...prev, phoneCountry: code };\n      console.log(\"تم تحديث بيانات النموذج (رمز الدولة):\", updated);\n      return updated;\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (localFormData == initialFormData) {\n      toast.error(\"يرجى تغيير أي حقل\");\n      return;\n    }\n    // التحقق من صحة بيانات النموذج\n    const formErrors = validateProfileForm(localFormData, true);\n\n    // دمج الأخطاء\n    const newErrors = { ...formErrors };\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      toast.error(\"يرجى تصحيح الأخطاء في النموذج\");\n      return;\n    }\n\n    setUpdateLoading(true);\n\n    // التحقق مما إذا كان البريد الإلكتروني قد تغير\n    const newEmail = localFormData.email;\n    const emailChanged = user && newEmail && newEmail !== user.email;\n\n    try {\n      // إنشاء نسخة جديدة من FormData\n      const formDataToSubmit = new FormData();\n\n      // إضافة الحقول الأساسية\n      formDataToSubmit.append(\"first_name\", localFormData.firstName);\n      formDataToSubmit.append(\"last_name\", localFormData.lastName);\n      formDataToSubmit.append(\"email\", localFormData.email);\n\n      // تنسيق رقم الهاتف مع رمز الدولة\n      const selectedCountry = countryCodes.find(\n        (c) => c.code === localFormData.phoneCountry\n      );\n      const dialCode = selectedCountry?.dialCode || \"\";\n      const phoneNumber = localFormData.phone || \"\";\n      const formattedPhone = phoneNumber\n        ? `${dialCode}${\n            phoneNumber.startsWith(\"0\") ? phoneNumber.substring(1) : phoneNumber\n          }`\n        : \"\";\n\n      // تسجيل رقم الهاتف للتصحيح\n      console.log(\"تنسيق رقم الهاتف:\", {\n        countryCode: localFormData.phoneCountry,\n        dialCode,\n        phoneNumber,\n        formattedPhone,\n      });\n\n      // استخدام كلا الحقلين phone و phone_number لضمان التوافق\n      formDataToSubmit.append(\"phone_number\", formattedPhone);\n      formDataToSubmit.append(\"phone\", formattedPhone);\n      formDataToSubmit.append(\"governorate\", localFormData.governorate || \"\");\n      formDataToSubmit.append(\"city\", localFormData.city || \"\");\n\n      // استدعاء دالة تحديث بيانات المستخدم\n      const response = await updateUserProfile(formDataToSubmit);\n\n      if (!user) {\n        setUpdateLoading(false);\n        return;\n      }\n\n      const updatedUser = {\n        ...user,\n        first_name: localFormData.firstName || user.first_name,\n        last_name: localFormData.lastName || user.last_name,\n        email: localFormData.email || user.email,\n        phone: formattedPhone || user.phone,\n        governorate: localFormData.governorate || \"\",\n        city: localFormData.city || \"\",\n      };\n\n      onUserUpdate(updatedUser);\n      setUpdateLoading(false);\n\n      // التحقق إذا كان البريد الإلكتروني تغير ويحتاج إلى تفعيل\n      if (\n        emailChanged &&\n        response &&\n        \"email_verified\" in response &&\n        !response.email_verified\n      ) {\n        // تم تغيير البريد الإلكتروني ويحتاج تفعيل - التوجيه لصفحة التفعيل\n        setTimeout(() => {\n          redirectToEmailVerification(\n            newEmail,\n            \"تم تحديث بريدك الإلكتروني ويجب التحقق منه. يرجى التحقق من صندوق الوارد الخاص بك لرمز التفعيل.\"\n          );\n        }, 1000);\n        return;\n      }\n\n      // عرض رسالة نجاح للتحديثات العادية\n      toast.success(\"تم تحديث الملف الشخصي بنجاح\", {\n        autoClose: 1500, // الإغلاق بعد 1.5 ثانية\n        hideProgressBar: true, // إزالة شريط التوقيت\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n\n      // إعادة تحميل الصفحة بعد 1.5 ثانية للتحديثات العادية الناجحة\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error: unknown) {\n      setUpdateLoading(false);\n      console.error(\"حدث خطأ أثناء تحديث الملف الشخصي:\", error);\n\n      // التحقق إذا كان الخطأ متعلق بتفعيل البريد الإلكتروني\n      if (isEmailVerificationError(error)) {\n        triggerEmailVerificationPopup(error);\n        return;\n      }\n\n      toast.error(\"فشل تحديث الملف الشخصي\");\n    }\n  };\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white\">معلومات الملف الشخصي</h3>\n        <p className=\"text-sm text-gray-300 mt-1\">تحديث بياناتك الشخصية</p>\n      </div>\n\n      <form\n        id=\"profileForm\"\n        onSubmit={handleSubmit}\n        className=\"p-8 space-y-8\"\n        noValidate\n      >\n        {/* قسم المعلومات الأساسية */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-gray-50 rounded-xl p-6 border border-gray-200\">\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-6 flex items-center\">\n              <UserIcon className=\"w-5 h-5 ml-2 text-gray-600\" />\n              المعلومات الأساسية\n            </h4>\n\n            <div className=\"space-y-4\">\n              {/* حقول الاسم */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Input\n                  id=\"firstName\"\n                  label=\"الاسم الأول\"\n                  name=\"firstName\"\n                  value={localFormData.firstName}\n                  onChange={(e) =>\n                    setLocalFormData({\n                      ...localFormData,\n                      firstName: e.target.value,\n                    })\n                  }\n                  error={!!errors.firstName}\n                  errorMessage={errors.firstName}\n                  required\n                />\n                <Input\n                  id=\"lastName\"\n                  label=\"اسم العائلة\"\n                  name=\"lastName\"\n                  value={localFormData.lastName}\n                  onChange={(e) =>\n                    setLocalFormData({\n                      ...localFormData,\n                      lastName: e.target.value,\n                    })\n                  }\n                  error={!!errors.lastName}\n                  errorMessage={errors.lastName}\n                  required\n                />\n              </div>\n\n              {/* البريد الإلكتروني */}\n              <Input\n                id=\"email\"\n                label=\"البريد الإلكتروني\"\n                type=\"email\"\n                name=\"email\"\n                value={localFormData.email}\n                onChange={(e) =>\n                  setLocalFormData({ ...localFormData, email: e.target.value })\n                }\n                error={!!errors.email}\n                errorMessage={errors.email}\n                required\n              />\n\n              {/* رقم الهاتف */}\n              <div className=\"space-y-2\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  رقم الهاتف\n                  <span className=\"text-red-500\">*</span>\n                </label>\n                <PhoneInput\n                  value={localFormData.phone || \"\"}\n                  countryCode={localFormData.phoneCountry || \"EG\"}\n                  onChange={handlePhoneChange}\n                  onCountryChange={handlePhoneCountryChange}\n                  error={!!errors.phone}\n                  errorMessage={errors.phone}\n                  countryCodes={countryCodes}\n                />\n                <p className=\"text-xs text-gray-500\">\n                  التنسيق الحالي: {localFormData.phoneCountry} (\n                  {countryCodes.find(\n                    (c) => c.code === localFormData.phoneCountry\n                  )?.dialCode || \"\"}\n                  ) {localFormData.phone}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* زر الحفظ */}\n        <div className=\"pt-8\">\n          <button\n            type=\"submit\"\n            disabled={updateLoading}\n            className=\"w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]\"\n          >\n            {updateLoading ? (\n              <>\n                <Loader2 className=\"w-5 h-5 animate-spin mr-2\" />\n                جاري تحديث الملف الشخصي...\n              </>\n            ) : (\n              \"تحديث الملف الشخصي\"\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ProfileForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAIA;AACA;AAEA;AACA;AAfA;;;;;;;;;;;AAqCA,MAAM,cAA0C,CAAC,EAC/C,eAAe,EACf,IAAI,EACJ,YAAY,EACb;IACC,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,OAAO,EAAE;YAC3B,IAAI,OAAO,gBAAgB,OAAO,KAAK,UAAU;gBAC/C,cAAc,gBAAgB,OAAO;YACvC,OAAO,IAAI,gBAAgB,OAAO,YAAY,MAAM;gBAClD,MAAM,MAAM,IAAI,eAAe,CAAC,gBAAgB,OAAO;gBACvD,cAAc;YAChB;QACF,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC,gBAAgB,OAAO;KAAC;IAE5B,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,cAAc,CAAC,WAAW,UAAU,CAAC,SAAS;gBAChD,IAAI,eAAe,CAAC;YACtB;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,wBAAwB;QACpC,iBAAiB,CAAC;YAChB,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAM;YACxC,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,iBAAiB,CAAC;YAChB,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,cAAc;YAAK;YAC9C,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,iBAAiB,iBAAiB;YACpC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,+BAA+B;QAC/B,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;QAEtD,cAAc;QACd,MAAM,YAAY;YAAE,GAAG,UAAU;QAAC;QAClC,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iBAAiB;QAEjB,+CAA+C;QAC/C,MAAM,WAAW,cAAc,KAAK;QACpC,MAAM,eAAe,QAAQ,YAAY,aAAa,KAAK,KAAK;QAEhE,IAAI;YACF,+BAA+B;YAC/B,MAAM,mBAAmB,IAAI;YAE7B,wBAAwB;YACxB,iBAAiB,MAAM,CAAC,cAAc,cAAc,SAAS;YAC7D,iBAAiB,MAAM,CAAC,aAAa,cAAc,QAAQ;YAC3D,iBAAiB,MAAM,CAAC,SAAS,cAAc,KAAK;YAEpD,iCAAiC;YACjC,MAAM,kBAAkB,2HAAA,CAAA,eAAY,CAAC,IAAI,CACvC,CAAC,IAAM,EAAE,IAAI,KAAK,cAAc,YAAY;YAE9C,MAAM,WAAW,iBAAiB,YAAY;YAC9C,MAAM,cAAc,cAAc,KAAK,IAAI;YAC3C,MAAM,iBAAiB,cACnB,GAAG,WACD,YAAY,UAAU,CAAC,OAAO,YAAY,SAAS,CAAC,KAAK,aACzD,GACF;YAEJ,2BAA2B;YAC3B,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,aAAa,cAAc,YAAY;gBACvC;gBACA;gBACA;YACF;YAEA,yDAAyD;YACzD,iBAAiB,MAAM,CAAC,gBAAgB;YACxC,iBAAiB,MAAM,CAAC,SAAS;YACjC,iBAAiB,MAAM,CAAC,eAAe,cAAc,WAAW,IAAI;YACpE,iBAAiB,MAAM,CAAC,QAAQ,cAAc,IAAI,IAAI;YAEtD,qCAAqC;YACrC,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;YAEzC,IAAI,CAAC,MAAM;gBACT,iBAAiB;gBACjB;YACF;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,YAAY,cAAc,SAAS,IAAI,KAAK,UAAU;gBACtD,WAAW,cAAc,QAAQ,IAAI,KAAK,SAAS;gBACnD,OAAO,cAAc,KAAK,IAAI,KAAK,KAAK;gBACxC,OAAO,kBAAkB,KAAK,KAAK;gBACnC,aAAa,cAAc,WAAW,IAAI;gBAC1C,MAAM,cAAc,IAAI,IAAI;YAC9B;YAEA,aAAa;YACb,iBAAiB;YAEjB,yDAAyD;YACzD,IACE,gBACA,YACA,oBAAoB,YACpB,CAAC,SAAS,cAAc,EACxB;gBACA,kEAAkE;gBAClE,WAAW;oBACT,CAAA,GAAA,+HAAA,CAAA,8BAA2B,AAAD,EACxB,UACA;gBAEJ,GAAG;gBACH;YACF;YAEA,mCAAmC;YACnC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B;gBAC3C,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,6DAA6D;YAC7D,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAgB;YACvB,iBAAiB;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YAEnD,sDAAsD;YACtD,IAAI,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE;gBAC9B;YACF;YAEA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,8OAAC;gBACC,IAAG;gBACH,UAAU;gBACV,WAAU;gBACV,UAAU;;kCAGV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,kMAAA,CAAA,OAAQ;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;8CAIrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qIAAA,CAAA,UAAK;oDACJ,IAAG;oDACH,OAAM;oDACN,MAAK;oDACL,OAAO,cAAc,SAAS;oDAC9B,UAAU,CAAC,IACT,iBAAiB;4DACf,GAAG,aAAa;4DAChB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC3B;oDAEF,OAAO,CAAC,CAAC,OAAO,SAAS;oDACzB,cAAc,OAAO,SAAS;oDAC9B,QAAQ;;;;;;8DAEV,8OAAC,qIAAA,CAAA,UAAK;oDACJ,IAAG;oDACH,OAAM;oDACN,MAAK;oDACL,OAAO,cAAc,QAAQ;oDAC7B,UAAU,CAAC,IACT,iBAAiB;4DACf,GAAG,aAAa;4DAChB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC1B;oDAEF,OAAO,CAAC,CAAC,OAAO,QAAQ;oDACxB,cAAc,OAAO,QAAQ;oDAC7B,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC,qIAAA,CAAA,UAAK;4CACJ,IAAG;4CACH,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,OAAO,cAAc,KAAK;4CAC1B,UAAU,CAAC,IACT,iBAAiB;oDAAE,GAAG,aAAa;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAE7D,OAAO,CAAC,CAAC,OAAO,KAAK;4CACrB,cAAc,OAAO,KAAK;4CAC1B,QAAQ;;;;;;sDAIV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;wDAA0C;sEAEzD,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAEjC,8OAAC,gIAAA,CAAA,UAAU;oDACT,OAAO,cAAc,KAAK,IAAI;oDAC9B,aAAa,cAAc,YAAY,IAAI;oDAC3C,UAAU;oDACV,iBAAiB;oDACjB,OAAO,CAAC,CAAC,OAAO,KAAK;oDACrB,cAAc,OAAO,KAAK;oDAC1B,cAAc,2HAAA,CAAA,eAAY;;;;;;8DAE5B,8OAAC;oDAAE,WAAU;;wDAAwB;wDAClB,cAAc,YAAY;wDAAC;wDAC3C,2HAAA,CAAA,eAAY,CAAC,IAAI,CAChB,CAAC,IAAM,EAAE,IAAI,KAAK,cAAc,YAAY,GAC3C,YAAY;wDAAG;wDACf,cAAc,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,8BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 2283, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/common/PasswordInput.tsx"], "sourcesContent": ["import React, { useState, InputHTMLAttributes } from \"react\";\nimport { <PERSON>, Eye, EyeOff, AlertCircle } from \"lucide-react\";\n\ninterface PasswordInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  id: string;\n  label?: string;\n  error?: boolean;\n  errorMessage?: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nconst PasswordInput: React.FC<PasswordInputProps> = ({\n  id,\n  label,\n  error,\n  value,\n  onChange,\n  className = \"\",\n  ...props\n}) => {\n  const [showPassword, setShowPassword] = useState(false);\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {label && (\n        <label\n          htmlFor={id}\n          className=\"block text-sm font-medium text-gray-700 mb-1\"\n        >\n          {label}\n        </label>\n      )}\n      <div className=\"relative\">\n        <input\n          id={id}\n          type={showPassword ? \"text\" : \"password\"}\n          className={`appearance-none text-right text-end rounded-lg relative block w-full px-3 py-3 pl-10 pr-10 border ${\n            error ? \"border-red-500\" : \"border-gray-300\"\n          } placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${className}`}\n          value={value}\n          onChange={onChange}\n          {...props}\n        />\n        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n          <Lock className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <button\n          type=\"button\"\n          className=\"absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none\"\n          onClick={togglePasswordVisibility}\n        >\n          {showPassword ? (\n            <EyeOff className=\"h-5 w-5\" />\n          ) : (\n            <Eye className=\"h-5 w-5\" />\n          )}\n        </button>\n        {error && (\n          <div className=\"absolute left-10 top-1/2 transform -translate-y-1/2 text-red-500\">\n            <AlertCircle className=\"h-5 w-5\" />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordInput;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAWA,MAAM,gBAA8C,CAAC,EACnD,EAAE,EACF,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,IAAI;wBACJ,MAAM,eAAe,SAAS;wBAC9B,WAAW,CAAC,kGAAkG,EAC5G,QAAQ,mBAAmB,kBAC5B,8GAA8G,EAAE,WAAW;wBAC5H,OAAO;wBACP,UAAU;wBACT,GAAG,KAAK;;;;;;kCAEX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS;kCAER,6BACC,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;oBAGlB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMnC;uCAEe", "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/components/PasswordStrengthIndicator.tsx"], "sourcesContent": ["import React from \"react\";\nimport { PasswordStrength } from \"../utils/formUtils\";\nimport { Check, X } from \"lucide-react\";\n\ninterface PasswordStrengthIndicatorProps {\n  strength: PasswordStrength;\n  password?: string;\n}\n\nconst PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({\n  strength,\n  password = \"\",\n}) => {\n  if (!strength) return null;\n\n  const getColorClass = () => {\n    switch (strength) {\n      case \"weak\":\n        return \"bg-red-500\";\n      case \"medium\":\n        return \"bg-yellow-500\";\n      case \"strong\":\n        return \"bg-green-500\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getWidthClass = () => {\n    switch (strength) {\n      case \"weak\":\n        return \"w-1/3\";\n      case \"medium\":\n        return \"w-2/3\";\n      case \"strong\":\n        return \"w-full\";\n      default:\n        return \"\";\n    }\n  };\n\n  // تحقق من شروط كلمة السر\n  const hasMinLength = password.length >= 6;\n  const hasUppercase = /[A-Z]/.test(password);\n  const hasLowercase = /[a-z]/.test(password);\n  const hasNumbers = /[0-9]/.test(password);\n  const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n\n  const renderCriteriaItem = (label: string, isMet: boolean) => (\n    <div className=\"flex items-center space-x-2\">\n      {isMet ? (\n        <Check className=\"h-3 w-3 text-green-500\" />\n      ) : (\n        <X className=\"h-3 w-3 text-red-500\" />\n      )}\n      <span className={`text-xs ${isMet ? \"text-green-600\" : \"text-gray-500\"}`}>\n        {label}\n      </span>\n    </div>\n  );\n\n  return (\n    <div className=\"!mt-4 space-y-2\">\n      <div className=\"h-1 w-full bg-gray-200 rounded-full overflow-hidden\">\n        <div\n          className={`h-full ${getColorClass()} ${getWidthClass()} transition-all duration-300`}\n        ></div>\n      </div>\n      <p\n        className={`text-xs ${\n          strength === \"weak\"\n            ? \"text-red-500\"\n            : strength === \"medium\"\n            ? \"text-yellow-500\"\n            : \"text-green-500\"\n        }`}\n      >\n        قوة كلمة المرور:{\" \"}\n        {strength === \"weak\"\n          ? \"ضعيفة\"\n          : strength === \"medium\"\n          ? \"متوسطة\"\n          : \"قوية\"}\n      </p>\n\n      <div className=\"bg-gray-50 p-2 rounded-md border border-gray-200 mt-2\">\n        <p className=\"text-xs font-medium text-gray-700 mb-1\">\n          متطلبات كلمة المرور:\n        </p>\n        <div className=\"grid grid-cols-2 gap-1\">\n          {renderCriteriaItem(\"6 أحرف على الأقل\", hasMinLength)}\n          {renderCriteriaItem(\"حرف كبير (A-Z)\", hasUppercase)}\n          {renderCriteriaItem(\"حرف صغير (a-z)\", hasLowercase)}\n          {renderCriteriaItem(\"رقم (0-9)\", hasNumbers)}\n          {renderCriteriaItem(\"رمز خاص (!@#$)\", hasSpecialChars)}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordStrengthIndicator;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAOA,MAAM,4BAAsE,CAAC,EAC3E,QAAQ,EACR,WAAW,EAAE,EACd;IACC,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,SAAS,MAAM,IAAI;IACxC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;IAClC,MAAM,aAAa,QAAQ,IAAI,CAAC;IAChC,MAAM,kBAAkB,yBAAyB,IAAI,CAAC;IAEtD,MAAM,qBAAqB,CAAC,OAAe,sBACzC,8OAAC;YAAI,WAAU;;gBACZ,sBACC,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;yCAEjB,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;8BAEf,8OAAC;oBAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,mBAAmB,iBAAiB;8BACrE;;;;;;;;;;;;IAKP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,4BAA4B,CAAC;;;;;;;;;;;0BAGzF,8OAAC;gBACC,WAAW,CAAC,QAAQ,EAClB,aAAa,SACT,iBACA,aAAa,WACb,oBACA,kBACJ;;oBACH;oBACkB;oBAChB,aAAa,SACV,UACA,aAAa,WACb,WACA;;;;;;;0BAGN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;;4BACZ,mBAAmB,oBAAoB;4BACvC,mBAAmB,kBAAkB;4BACrC,mBAAmB,kBAAkB;4BACrC,mBAAmB,aAAa;4BAChC,mBAAmB,kBAAkB;;;;;;;;;;;;;;;;;;;AAKhD;uCAEe", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/PasswordForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Lock, Loader2 } from \"lucide-react\";\nimport PasswordInput from \"@/components/common/PasswordInput\";\nimport PasswordStrengthIndicator from \"@/components/PasswordStrengthIndicator\";\n\nimport {\n  FormErrors,\n  PasswordStrength,\n  checkPasswordStrength,\n} from \"@/utils/formUtils\";\n\nexport interface PasswordFormData {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\ninterface PasswordFormProps {\n  onSubmit: (data: {\n    currentPassword: string;\n    newPassword: string;\n  }) => Promise<void>;\n  loading: boolean;\n}\n\nconst PasswordForm: React.FC<PasswordFormProps> = ({\n  onSubmit,\n  loading: externalLoading,\n}) => {\n  const [formData, setFormData] = useState<PasswordFormData>({\n    currentPassword: \"\",\n    newPassword: \"\",\n    confirmPassword: \"\",\n  });\n  const [internalLoading, setInternalLoading] = useState(false);\n  const loading = externalLoading || internalLoading;\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [passwordStrength, setPasswordStrength] =\n    useState<PasswordStrength>(\"\");\n\n  useEffect(() => {\n    const strength = formData.newPassword\n      ? checkPasswordStrength(formData.newPassword)\n      : \"\";\n    setPasswordStrength(strength);\n  }, [formData.newPassword]);\n\n  const validateForm = (data: PasswordFormData): FormErrors => {\n    const errors: FormErrors = {};\n\n    if (!data.currentPassword) {\n      errors.currentPassword = \"كلمة المرور الحالية مطلوبة\";\n    }\n\n    if (!data.newPassword) {\n      errors.newPassword = \"كلمة المرور الجديدة مطلوبة\";\n    } else if (data.newPassword.length < 6) {\n      errors.newPassword = \"يجب أن تتكون كلمة المرور من 6 أحرف على الأقل\";\n    } else {\n      const strength = checkPasswordStrength(data.newPassword);\n      if (strength === \"weak\") {\n        errors.newPassword =\n          \"كلمة المرور ضعيفة جدًا. يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام\";\n      }\n    }\n\n    if (!data.confirmPassword) {\n      errors.confirmPassword = \"يرجى تأكيد كلمة المرور الجديدة\";\n    } else if (data.newPassword !== data.confirmPassword) {\n      errors.confirmPassword = \"كلمات المرور غير متطابقة\";\n    }\n\n    return errors;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    const newErrors = validateForm(formData);\n    setErrors(newErrors);\n\n    if (Object.keys(newErrors).length > 0) {\n      if (Object.keys(newErrors).length === 1) {\n        const firstErrorKey = Object.keys(newErrors)[0];\n        const firstErrorMessage = newErrors[firstErrorKey as keyof FormErrors];\n        if (firstErrorMessage) {\n          toast.error(firstErrorMessage, {\n            autoClose: false,\n            hideProgressBar: true,\n            closeOnClick: true,\n            pauseOnHover: false,\n            draggable: true,\n          });\n        }\n      } else {\n        toast.error(\"يرجى تصحيح الأخطاء في النموذج\", {\n          autoClose: false,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        });\n      }\n      return;\n    }\n\n    const loadingToastId = toast.loading(\"جاري تغيير كلمة المرور...\", {\n      hideProgressBar: true,\n      closeOnClick: false,\n      pauseOnHover: false,\n      draggable: false,\n    });\n    setInternalLoading(true);\n\n    try {\n      await onSubmit({\n        currentPassword: formData.currentPassword,\n        newPassword: formData.newPassword,\n      });\n\n      toast.dismiss(loadingToastId);\n    } catch (error) {\n      console.error(\"خطأ في تغيير كلمة المرور:\", error);\n      toast.dismiss(loadingToastId);\n      toast.error(\"فشل تغيير كلمة المرور\", {\n        autoClose: false,\n        hideProgressBar: true,\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n    } finally {\n      setInternalLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white\">تغيير كلمة المرور</h3>\n        <p className=\"text-sm text-gray-300 mt-1\">تحديث كلمة مرور حسابك</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"p-8 space-y-8\">\n        <div className=\"bg-gray-50 rounded-xl p-6 border border-gray-200\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-6 flex items-center\">\n            <Lock className=\"w-5 h-5 ml-2 text-gray-600\" />\n            معلومات كلمة المرور\n          </h4>\n\n          <div className=\"space-y-6\">\n            <PasswordInput\n              id=\"currentPassword\"\n              name=\"currentPassword\"\n              placeholder=\"كلمة المرور الحالية\"\n              value={formData.currentPassword}\n              onChange={handleInputChange}\n              error={!!errors.currentPassword}\n              autoComplete=\"current-password\"\n            />\n\n            <div className=\"space-y-1\">\n              <PasswordInput\n                id=\"newPassword\"\n                name=\"newPassword\"\n                placeholder=\"كلمة المرور الجديدة\"\n                value={formData.newPassword}\n                onChange={handleInputChange}\n                error={!!errors.newPassword}\n                autoComplete=\"new-password\"\n              />\n\n              {formData.newPassword && (\n                <PasswordStrengthIndicator\n                  strength={passwordStrength}\n                  password={formData.newPassword}\n                />\n              )}\n            </div>\n\n            <PasswordInput\n              id=\"confirmPassword\"\n              name=\"confirmPassword\"\n              placeholder=\"تأكيد كلمة المرور الجديدة\"\n              value={formData.confirmPassword}\n              onChange={handleInputChange}\n              error={!!errors.confirmPassword}\n              autoComplete=\"new-password\"\n            />\n          </div>\n        </div>\n\n        <div className=\"pt-8\">\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]\"\n          >\n            {loading ? (\n              <>\n                <Loader2 className=\"w-5 h-5 mr-2 animate-spin\" />\n                جاري تغيير كلمة المرور...\n              </>\n            ) : (\n              <>\n                <Lock className=\"w-5 h-5 mr-2\" />\n                تغيير كلمة المرور\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default PasswordForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AARA;;;;;;;;AA4BA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,SAAS,eAAe,EACzB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,UAAU,mBAAmB;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,SAAS,WAAW,GACjC,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,WAAW,IAC1C;QACJ,oBAAoB;IACtB,GAAG;QAAC,SAAS,WAAW;KAAC;IAEzB,MAAM,eAAe,CAAC;QACpB,MAAM,SAAqB,CAAC;QAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,eAAe,GAAG;QAC3B;QAEA,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;YACtC,OAAO,WAAW,GAAG;QACvB,OAAO;YACL,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,WAAW;YACvD,IAAI,aAAa,QAAQ;gBACvB,OAAO,WAAW,GAChB;YACJ;QACF;QAEA,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,eAAe,GAAG;QAC3B,OAAO,IAAI,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;YACpD,OAAO,eAAe,GAAG;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,YAAY,aAAa;QAC/B,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK,GAAG;gBACvC,MAAM,gBAAgB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC/C,MAAM,oBAAoB,SAAS,CAAC,cAAkC;gBACtE,IAAI,mBAAmB;oBACrB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;wBAC7B,WAAW;wBACX,iBAAiB;wBACjB,cAAc;wBACd,cAAc;wBACd,WAAW;oBACb;gBACF;YACF,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;oBAC3C,WAAW;oBACX,iBAAiB;oBACjB,cAAc;oBACd,cAAc;oBACd,WAAW;gBACb;YACF;YACA;QACF;QAEA,MAAM,iBAAiB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;YAChE,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,WAAW;QACb;QACA,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS;gBACb,iBAAiB,SAAS,eAAe;gBACzC,aAAa,SAAS,WAAW;YACnC;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;QACF,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6IAAA,CAAA,UAAa;wCACZ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,eAAe;wCAC/B,cAAa;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6IAAA,CAAA,UAAa;gDACZ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,OAAO,CAAC,CAAC,OAAO,WAAW;gDAC3B,cAAa;;;;;;4CAGd,SAAS,WAAW,kBACnB,8OAAC,+IAAA,CAAA,UAAyB;gDACxB,UAAU;gDACV,UAAU,SAAS,WAAW;;;;;;;;;;;;kDAKpC,8OAAC,6IAAA,CAAA,UAAa;wCACZ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,eAAe;wCAC/B,cAAa;;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;uCAEe", "debugId": null}}, {"offset": {"line": 2842, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/AdminPanel.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  <PERSON>,\n  Shield,\n  CheckCircle,\n  XCircle,\n  Trash2,\n  Loader2,\n  Search,\n  Filter,\n  Eye,\n} from \"lucide-react\";\n\ninterface User {\n  id: number;\n  first_name: string;\n  last_name: string;\n  email: string;\n  phone_number: string;\n  city: string;\n  governorate: string;\n  is_approved: boolean;\n  is_admin: boolean;\n  is_email_verified: boolean;\n  date_joined: string;\n  approved_by_email?: string;\n  approved_at?: string;\n  id_image?: string;\n  children: Array<{\n    name: string;\n    class: string;\n    stage: string;\n  }>;\n}\n\ninterface AdminPanelProps {\n  currentUser: any;\n}\n\nconst AdminPanel: React.FC<AdminPanelProps> = ({ currentUser }) => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [actionLoading, setActionLoading] = useState<{\n    [key: number]: boolean;\n  }>({});\n  const [activeTab, setActiveTab] = useState<\"users\" | \"permissions\">(\"users\");\n\n  useEffect(() => {\n    if (currentUser?.is_admin) {\n      fetchUsers();\n    }\n  }, [currentUser]);\n\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        \"https://api.imdadport.com/api/admin/users/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setUsers(data);\n      } else {\n        toast.error(\"فشل في تحميل بيانات المستخدمين\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching users:\", error);\n      toast.error(\"فشل في تحميل بيانات المستخدمين\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApproveUser = async (userId: number) => {\n    setActionLoading((prev) => ({ ...prev, [userId]: true }));\n    try {\n      const response = await fetch(\n        `https://api.imdadport.com/api/admin/users/${userId}/approve/`,\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        toast.success(\"تم اعتماد المستخدم بنجاح\");\n        fetchUsers(); // Refresh the list\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || \"فشل في اعتماد المستخدم\");\n      }\n    } catch (error) {\n      console.error(\"Error approving user:\", error);\n      toast.error(\"فشل في اعتماد المستخدم\");\n    } finally {\n      setActionLoading((prev) => ({ ...prev, [userId]: false }));\n    }\n  };\n\n  const handleDeleteUser = async (userId: number) => {\n    if (\n      !confirm(\n        \"هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.\"\n      )\n    ) {\n      return;\n    }\n\n    setActionLoading((prev) => ({ ...prev, [userId]: true }));\n    try {\n      const response = await fetch(\n        `https://api.imdadport.com/api/admin/users/${userId}/delete/`,\n        {\n          method: \"DELETE\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        toast.success(\"تم حذف المستخدم بنجاح\");\n        fetchUsers(); // Refresh the list\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || \"فشل في حذف المستخدم\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n      toast.error(\"فشل في حذف المستخدم\");\n    } finally {\n      setActionLoading((prev) => ({ ...prev, [userId]: false }));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  };\n\n  if (!currentUser?.is_admin) {\n    return (\n      <div\n        className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n        dir=\"rtl\"\n      >\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <h3 className=\"text-xl font-bold text-white\">لوحة الإدارة</h3>\n          <p className=\"text-sm text-gray-300 mt-1\">غير مصرح لك بالوصول</p>\n        </div>\n        <div className=\"p-8\">\n          <div className=\"text-center\">\n            <Shield className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              غير مصرح لك بالوصول\n            </h3>\n            <p className=\"text-gray-600\">\n              تحتاج إلى صلاحيات إدارية للوصول إلى هذه الصفحة\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white\">لوحة الإدارة</h3>\n        <p className=\"text-sm text-gray-300 mt-1\">إدارة المستخدمين والنظام</p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 space-x-reverse px-6\">\n          <button\n            onClick={() => setActiveTab(\"users\")}\n            className={`py-4 px-1 border-b-2 font-medium text-sm ${\n              activeTab === \"users\"\n                ? \"border-black text-black\"\n                : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"\n            }`}\n          >\n            <Users className=\"w-4 h-4 inline-block ml-2\" />\n            إدارة المستخدمين\n          </button>\n          <button\n            onClick={() => setActiveTab(\"permissions\")}\n            className={`py-4 px-1 border-b-2 font-medium text-sm ${\n              activeTab === \"permissions\"\n                ? \"border-black text-black\"\n                : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"\n            }`}\n          >\n            <Shield className=\"w-4 h-4 inline-block ml-2\" />\n            إدارة التصاريح\n          </button>\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-8\">\n        {activeTab === \"users\" && (\n          <div>\n            {/* Statistics */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <Users className=\"w-8 h-8 text-blue-600\" />\n                  <div className=\"mr-3\">\n                    <p className=\"text-sm font-medium text-blue-600\">\n                      إجمالي المستخدمين\n                    </p>\n                    <p className=\"text-2xl font-bold text-blue-900\">\n                      {users.length}\n                    </p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <CheckCircle className=\"w-8 h-8 text-yellow-600\" />\n                  <div className=\"mr-3\">\n                    <p className=\"text-sm font-medium text-yellow-600\">\n                      في انتظار الموافقة\n                    </p>\n                    <p className=\"text-2xl font-bold text-yellow-900\">\n                      {users.filter((user) => !user.is_approved).length}\n                    </p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <Shield className=\"w-8 h-8 text-green-600\" />\n                  <div className=\"mr-3\">\n                    <p className=\"text-sm font-medium text-green-600\">\n                      المستخدمون المعتمدون\n                    </p>\n                    <p className=\"text-2xl font-bold text-green-900\">\n                      {users.filter((user) => user.is_approved).length}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Users Table */}\n            {loading ? (\n              <div className=\"flex items-center justify-center h-64\">\n                <div className=\"text-center\">\n                  <Loader2 className=\"w-8 h-8 animate-spin mx-auto mb-4 text-gray-600\" />\n                  <p className=\"text-gray-600\">جاري تحميل المستخدمين...</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        المستخدم\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        معلومات الاتصال\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الحالة\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        تاريخ التسجيل\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        الإجراءات\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                                <span className=\"text-sm font-medium text-gray-700\">\n                                  {user.first_name.charAt(0)}\n                                  {user.last_name.charAt(0)}\n                                </span>\n                              </div>\n                            </div>\n                            <div className=\"mr-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">\n                                {user.first_name} {user.last_name}\n                              </div>\n                              <div className=\"text-sm text-gray-500\">\n                                {user.email}\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm text-gray-900\">\n                            {user.phone_number}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {user.city}, {user.governorate}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span\n                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                                user.is_approved\n                                  ? \"bg-green-100 text-green-800\"\n                                  : \"bg-yellow-100 text-yellow-800\"\n                              }`}\n                            >\n                              {user.is_approved\n                                ? \"معتمد\"\n                                : \"في انتظار الموافقة\"}\n                            </span>\n                            {user.is_admin && (\n                              <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                                مدير\n                              </span>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {formatDate(user.date_joined)}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <div className=\"flex space-x-2 space-x-reverse\">\n                            {!user.is_approved && currentUser.is_superuser && (\n                              <button\n                                onClick={() => handleApproveUser(user.id)}\n                                disabled={actionLoading[user.id]}\n                                className=\"text-green-600 hover:text-green-900 disabled:opacity-50\"\n                              >\n                                {actionLoading[user.id] ? (\n                                  <Loader2 className=\"w-4 h-4 animate-spin\" />\n                                ) : (\n                                  <CheckCircle className=\"w-4 h-4\" />\n                                )}\n                              </button>\n                            )}\n                            {!user.is_admin && currentUser.is_superuser && (\n                              <button\n                                onClick={() => handleDeleteUser(user.id)}\n                                disabled={actionLoading[user.id]}\n                                className=\"text-red-600 hover:text-red-900 disabled:opacity-50\"\n                              >\n                                {actionLoading[user.id] ? (\n                                  <Loader2 className=\"w-4 h-4 animate-spin\" />\n                                ) : (\n                                  <Trash2 className=\"w-4 h-4\" />\n                                )}\n                              </button>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === \"permissions\" && (\n          <div className=\"text-center py-12\">\n            <Shield className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              إدارة التصاريح\n            </h3>\n            <p className=\"text-gray-600\">ستتم إضافة هذه الميزة قريباً</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AA0CA,MAAM,aAAwC,CAAC,EAAE,WAAW,EAAE;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,UAAU;YACzB;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,aAAa;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,8CACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,iBAAiB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,OAAO,EAAE;YAAK,CAAC;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,0CAA0C,EAAE,OAAO,SAAS,CAAC,EAC9D;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,cAAc,mBAAmB;YACnC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,OAAO,EAAE;gBAAM,CAAC;QAC1D;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IACE,CAAC,QACC,uEAEF;YACA;QACF;QAEA,iBAAiB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,OAAO,EAAE;YAAK,CAAC;QACvD,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,0CAA0C,EAAE,OAAO,QAAQ,CAAC,EAC7D;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,cAAc,mBAAmB;YACnC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,OAAO,EAAE;gBAAM,CAAC;QAC1D;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,CAAC,aAAa,UAAU;QAC1B,qBACE,8OAAC;YACC,WAAU;YACV,KAAI;;8BAEJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAE5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAOvC;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,4BACA,8EACJ;;8CAEF,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAGjD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,gBACV,4BACA,8EACJ;;8CAEF,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,yBACb,8OAAC;;0CAEC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEAGjD,8OAAC;4DAAE,WAAU;sEACV,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAKrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEAGnD,8OAAC;4DAAE,WAAU;sEACV,MAAM,MAAM,CAAC,CAAC,OAAS,CAAC,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;kDAKzD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAGlD,8OAAC;4DAAE,WAAU;sEACV,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQzD,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;qDAIjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;kEAGhG,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,8OAAC;4CAAM,WAAU;sDACd,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAK,WAAU;;oFACb,KAAK,UAAU,CAAC,MAAM,CAAC;oFACvB,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;kFAI7B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFACZ,KAAK,UAAU;oFAAC;oFAAE,KAAK,SAAS;;;;;;;0FAEnC,8OAAC;gFAAI,WAAU;0FACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EACZ,KAAK,YAAY;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;;wEACZ,KAAK,IAAI;wEAAC;wEAAG,KAAK,WAAW;;;;;;;;;;;;;sEAGlC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,WAAW,CAAC,yDAAyD,EACnE,KAAK,WAAW,GACZ,gCACA,iCACJ;kFAED,KAAK,WAAW,GACb,UACA;;;;;;oEAEL,KAAK,QAAQ,kBACZ,8OAAC;wEAAK,WAAU;kFAAqF;;;;;;;;;;;;;;;;;sEAM3G,8OAAC;4DAAG,WAAU;sEACX,WAAW,KAAK,WAAW;;;;;;sEAE9B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,KAAK,WAAW,IAAI,YAAY,YAAY,kBAC5C,8OAAC;wEACC,SAAS,IAAM,kBAAkB,KAAK,EAAE;wEACxC,UAAU,aAAa,CAAC,KAAK,EAAE,CAAC;wEAChC,WAAU;kFAET,aAAa,CAAC,KAAK,EAAE,CAAC,iBACrB,8OAAC,iNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;iGAEnB,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;oEAI5B,CAAC,KAAK,QAAQ,IAAI,YAAY,YAAY,kBACzC,8OAAC;wEACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;wEACvC,UAAU,aAAa,CAAC,KAAK,EAAE,CAAC;wEAChC,WAAU;kFAET,aAAa,CAAC,KAAK,EAAE,CAAC,iBACrB,8OAAC,iNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;iGAEnB,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDA5ErB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA2F7B,cAAc,+BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;uCAEe", "debugId": null}}, {"offset": {"line": 3648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/BalanceForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { CreditCard, Loader2, Upload, Copy, CheckCircle } from \"lucide-react\";\nimport Input from \"@/components/common/Input\";\nimport { ExtendedUser } from \"../page\";\n\ninterface BalanceFormData {\n  name: string;\n  amount: string;\n  notes: string;\n  transactionFile: File | null;\n}\n\ninterface FormErrors {\n  name?: string;\n  amount?: string;\n  notes?: string;\n  transactionFile?: string;\n}\n\nconst BalanceForm: React.FC = ({ user }: { user: ExtendedUser }) => {\n  const [formData, setFormData] = useState<BalanceFormData>({\n    name: \"\",\n    amount: \"\",\n    notes: \"\",\n    transactionFile: null,\n  });\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const [copiedField, setCopiedField] = useState<string | null>(null);\n\n  // Bank account details\n  const bankDetails = {\n    bankName: \"مصرف الراجحي - alrajhi bank\",\n    accountNumber: \"395000010006086118542\",\n    accountName: \"مؤسسة باب الامداد للتجارة\",\n    iban: \"************************\",\n    swiftCode: \"RJHISARI\",\n    sudanBankName: \"بنك الخرطوم (بنكك)\",\n    sudanIban: \"******************\",\n    sudanAccountNumber: \"4153737\",\n    sudanAccountName: \"محمد الفاتح عبدالرحمن جمعة فضل\",\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = \"الاسم مطلوب\";\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = \"الاسم قصير جداً\";\n    }\n\n    if (!formData.amount.trim()) {\n      newErrors.amount = \"المبلغ مطلوب\";\n    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {\n      newErrors.amount = \"يرجى إدخال مبلغ صحيح\";\n    }\n\n    if (!formData.transactionFile) {\n      newErrors.transactionFile = \"ملف التحويل مطلوب\";\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleInputChange = (field: keyof BalanceFormData, value: string) => {\n    setFormData((prev) => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setErrors((prev) => ({\n          ...prev,\n          transactionFile: \"حجم الملف يجب أن يكون أقل من 5 ميجابايت\",\n        }));\n        return;\n      }\n      setFormData((prev) => ({ ...prev, transactionFile: file }));\n      setErrors((prev) => ({ ...prev, transactionFile: undefined }));\n      // Create preview for images only\n      if (file.type.startsWith(\"image/\")) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          setImagePreview(e.target?.result as string);\n        };\n        reader.readAsDataURL(file);\n      } else {\n        setImagePreview(null);\n      }\n    }\n  };\n\n  const copyToClipboard = async (text: string, field: string) => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedField(field);\n      toast.success(\"تم نسخ البيانات بنجاح\", {\n        autoClose: 1500,\n        hideProgressBar: true,\n      });\n      setTimeout(() => setCopiedField(null), 2000);\n    } catch {\n      toast.error(\"فشل في نسخ البيانات\");\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      toast.error(\"يرجى تصحيح الأخطاء في النموذج\");\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Create FormData for file upload\n      const submitData = new FormData();\n      submitData.append(\"name\", formData.name);\n      submitData.append(\"amount\", formData.amount);\n      submitData.append(\"notes\", formData.notes);\n      if (formData.transactionFile) {\n        submitData.append(\"transaction_file\", formData.transactionFile);\n      }\n\n      // Submit to API\n      const response = await fetch(\n        \"https://api.imdadport.com/api/balance/request/\",\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n          body: submitData,\n        }\n      );\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"فشل في إرسال طلب إضافة الرصيد\");\n      }\n\n      toast.success(\"تم إرسال طلب إضافة الرصيد بنجاح\", {\n        autoClose: 3000,\n        hideProgressBar: true,\n      });\n\n      // Reset form\n      setFormData({\n        name: \"\",\n        amount: \"\",\n        notes: \"\",\n        transactionFile: null,\n      });\n      setImagePreview(null);\n    } catch {\n      toast.error(\"فشل في إرسال طلب إضافة الرصيد\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white\">إضافة رصيد</h3>\n        <p className=\"text-sm text-gray-300 mt-1\">\n          قم بتحويل المبلغ المطلوب وأرفق إيصال التحويل\n        </p>\n      </div>\n\n      <div className=\"p-8 space-y-8\">\n        {/* Bank Details Section */}\n        <div className=\"bg-gray-50 rounded-xl p-6 border border-gray-200\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n            <CreditCard className=\"w-5 h-5 ml-2\" />\n            بيانات الحساب البنكي\n          </h4>\n\n          <div className=\"flex flex-col gap-3\">\n            <div className=\"flex flex-wrap gap-3\">\n              <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">اسم البنك (السعودية)</p>\n                  <p className=\"font-medium text-gray-900\">\n                    {bankDetails.bankName}\n                  </p>\n                </div>\n                <button\n                  onClick={() =>\n                    copyToClipboard(bankDetails.bankName, \"bankName\")\n                  }\n                  className=\"text-gray-500 hover:text-black transition-colors\"\n                >\n                  {copiedField === \"bankName\" ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n\n              <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">رقم الحساب (السعودية)</p>\n                  <p className=\"font-medium text-gray-900 font-mono\">\n                    {bankDetails.accountNumber}\n                  </p>\n                </div>\n                <button\n                  onClick={() =>\n                    copyToClipboard(bankDetails.accountNumber, \"accountNumber\")\n                  }\n                  className=\"text-gray-500 hover:text-black transition-colors\"\n                >\n                  {copiedField === \"accountNumber\" ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n\n              <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">\n                    اسم صاحب الحساب (السعودية)\n                  </p>\n                  <p className=\"font-medium text-gray-900\">\n                    {bankDetails.accountName}\n                  </p>\n                </div>\n                <button\n                  onClick={() =>\n                    copyToClipboard(bankDetails.accountName, \"accountName\")\n                  }\n                  className=\"text-gray-500 hover:text-black transition-colors\"\n                >\n                  {copiedField === \"accountName\" ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n              <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">\n                    رقم الآيبان (السعودية)\n                  </p>\n                  <p className=\"font-medium text-gray-900 font-mono\">\n                    {bankDetails.iban}\n                  </p>\n                </div>\n                <button\n                  onClick={() => copyToClipboard(bankDetails.iban, \"iban\")}\n                  className=\"text-gray-500 hover:text-black transition-colors\"\n                >\n                  {copiedField === \"iban\" ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n\n              <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border w-full    md:w-[calc(50%-6px)]\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">\n                    رمز السويفت (السعودية)\n                  </p>\n                  <p className=\"font-medium text-gray-900 font-mono\">\n                    {bankDetails.swiftCode}\n                  </p>\n                </div>\n                <button\n                  onClick={() =>\n                    copyToClipboard(bankDetails.swiftCode, \"swiftCode\")\n                  }\n                  className=\"text-gray-500 hover:text-black transition-colors\"\n                >\n                  {copiedField === \"swiftCode\" ? (\n                    <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  ) : (\n                    <Copy className=\"w-4 h-4\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            {user?.phone_number.startsWith(\"+966\") && (\n              <div className=\"flex flex-wrap gap-3\">\n                <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border w-full  md:w-[calc(50%-6px)]\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">اسم البنك (السودان)</p>\n                    <p className=\"font-medium text-gray-900\">\n                      {bankDetails.sudanBankName}\n                    </p>\n                  </div>\n                  <button\n                    onClick={() =>\n                      copyToClipboard(\n                        bankDetails.sudanBankName,\n                        \"sudanBankName\"\n                      )\n                    }\n                    className=\"text-gray-500 hover:text-black transition-colors\"\n                  >\n                    {copiedField === \"sudanBankName\" ? (\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                    ) : (\n                      <Copy className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                </div>\n\n                <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">\n                      رقم الآيبان (السودان)\n                    </p>\n                    <p className=\"font-medium text-gray-900 font-mono\">\n                      {bankDetails.sudanIban}\n                    </p>\n                  </div>\n                  <button\n                    onClick={() =>\n                      copyToClipboard(bankDetails.sudanIban, \"sudanIban\")\n                    }\n                    className=\"text-gray-500 hover:text-black transition-colors\"\n                  >\n                    {copiedField === \"sudanIban\" ? (\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                    ) : (\n                      <Copy className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                </div>\n\n                <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">\n                      رقم الحساب بنكك (السودان)\n                    </p>\n                    <p className=\"font-medium text-gray-900 font-mono\">\n                      {bankDetails.sudanAccountNumber}\n                    </p>\n                  </div>\n                  <button\n                    onClick={() =>\n                      copyToClipboard(\n                        bankDetails.sudanAccountNumber,\n                        \"sudanAccountNumber\"\n                      )\n                    }\n                    className=\"text-gray-500 hover:text-black transition-colors\"\n                  >\n                    {copiedField === \"sudanAccountNumber\" ? (\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                    ) : (\n                      <Copy className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                </div>\n\n                <div className=\"flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]\">\n                  <div>\n                    <p className=\"text-sm text-gray-600\">\n                      اسم صاحب الحساب (السودان)\n                    </p>\n                    <p className=\"font-medium text-gray-900\">\n                      {bankDetails.sudanAccountName}\n                    </p>\n                  </div>\n                  <button\n                    onClick={() =>\n                      copyToClipboard(\n                        bankDetails.sudanAccountName,\n                        \"sudanAccountName\"\n                      )\n                    }\n                    className=\"text-gray-500 hover:text-black transition-colors\"\n                  >\n                    {copiedField === \"sudanAccountName\" ? (\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                    ) : (\n                      <Copy className=\"w-4 h-4\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Form Section */}\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <Input\n              id=\"name\"\n              label=\"الاسم\"\n              type=\"text\"\n              placeholder=\"اسم المرسل كما يظهر في إيصال التحويل\"\n              value={formData.name}\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\n              error={!!errors.name}\n              errorMessage={errors.name}\n              required\n            />\n\n            <Input\n              id=\"amount\"\n              label=\"المبلغ المحول\"\n              type=\"number\"\n              placeholder=\"مثال: 1000\"\n              value={formData.amount}\n              onChange={(e) => handleInputChange(\"amount\", e.target.value)}\n              error={!!errors.amount}\n              errorMessage={errors.amount}\n              required\n            />\n\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                ملف التحويل\n                <span className=\"text-red-500\">*</span>\n              </label>\n              <div className=\"relative\">\n                <input\n                  type=\"file\"\n                  id=\"transactionFile\"\n                  onChange={handleFileChange}\n                  className=\"hidden\"\n                />\n                <label\n                  htmlFor=\"transactionFile\"\n                  className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${\n                    errors.transactionFile\n                      ? \"border-red-300 bg-red-50\"\n                      : \"border-gray-300 bg-gray-50 hover:bg-gray-100\"\n                  }`}\n                >\n                  <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                    <Upload className=\"w-8 h-8 mb-2 text-gray-500\" />\n                    <p className=\"text-sm text-gray-500\">\n                      {formData.transactionFile\n                        ? formData.transactionFile.name\n                        : \"اختر ملف التحويل (صورة أو PDF أو أي نوع)\"}\n                    </p>\n                  </div>\n                </label>\n              </div>\n              {errors.transactionFile && (\n                <p className=\"text-sm text-red-600\">{errors.transactionFile}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Image Preview */}\n          {imagePreview && (\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                معاينة الصورة\n              </label>\n              <div className=\"relative inline-block\">\n                <img\n                  src={imagePreview}\n                  alt=\"معاينة إيصال التحويل\"\n                  className=\"max-w-xs max-h-48 rounded-lg border border-gray-300\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setFormData((prev) => ({\n                      ...prev,\n                      transactionFile: null,\n                    }));\n                    setImagePreview(null);\n                  }}\n                  className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors\"\n                >\n                  ×\n                </button>\n              </div>\n            </div>\n          )}\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              ملاحظات (اختياري)\n            </label>\n            <textarea\n              id=\"notes\"\n              value={formData.notes}\n              onChange={(e) => handleInputChange(\"notes\", e.target.value)}\n              placeholder=\"أي ملاحظات إضافية حول التحويل...\"\n              rows={4}\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n            />\n          </div>\n          <div className=\"pt-8\">\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]\"\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"w-5 h-5 animate-spin ml-2\" />\n                  جاري الإرسال...\n                </>\n              ) : (\n                \"إرسال طلب إضافة الرصيد\"\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default BalanceForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAsBA,MAAM,cAAwB,CAAC,EAAE,IAAI,EAA0B;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM;QACN,QAAQ;QACR,OAAO;QACP,iBAAiB;IACnB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,uBAAuB;IACvB,MAAM,cAAc;QAClB,UAAU;QACV,eAAe;QACf,aAAa;QACb,MAAM;QACN,WAAW;QACX,eAAe;QACf,WAAW;QACX,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI;YAC3B,UAAU,MAAM,GAAG;QACrB,OAAO,IAAI,MAAM,OAAO,SAAS,MAAM,MAAM,OAAO,SAAS,MAAM,KAAK,GAAG;YACzE,UAAU,MAAM,GAAG;QACrB;QAEA,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,+BAA+B;YAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,UAAU,CAAC,OAAS,CAAC;wBACnB,GAAG,IAAI;wBACP,iBAAiB;oBACnB,CAAC;gBACD;YACF;YACA,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,iBAAiB;gBAAK,CAAC;YACzD,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,iBAAiB;gBAAU,CAAC;YAC5D,iCAAiC;YACjC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAClC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,gBAAgB,EAAE,MAAM,EAAE;gBAC5B;gBACA,OAAO,aAAa,CAAC;YACvB,OAAO;gBACL,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,eAAe;YACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;gBACrC,WAAW;gBACX,iBAAiB;YACnB;YACA,WAAW,IAAM,eAAe,OAAO;QACzC,EAAE,OAAM;YACN,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,kCAAkC;YAClC,MAAM,aAAa,IAAI;YACvB,WAAW,MAAM,CAAC,QAAQ,SAAS,IAAI;YACvC,WAAW,MAAM,CAAC,UAAU,SAAS,MAAM;YAC3C,WAAW,MAAM,CAAC,SAAS,SAAS,KAAK;YACzC,IAAI,SAAS,eAAe,EAAE;gBAC5B,WAAW,MAAM,CAAC,oBAAoB,SAAS,eAAe;YAChE;YAEA,gBAAgB;YAChB,MAAM,WAAW,MAAM,MACrB,kDACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;gBACA,MAAM;YACR;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mCAAmC;gBAC/C,WAAW;gBACX,iBAAiB;YACnB;YAEA,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,iBAAiB;YACnB;YACA,gBAAgB;QAClB,EAAE,OAAM;YACN,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,QAAQ;;;;;;;;;;;;kEAGzB,8OAAC;wDACC,SAAS,IACP,gBAAgB,YAAY,QAAQ,EAAE;wDAExC,WAAU;kEAET,gBAAgB,2BACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,aAAa;;;;;;;;;;;;kEAG9B,8OAAC;wDACC,SAAS,IACP,gBAAgB,YAAY,aAAa,EAAE;wDAE7C,WAAU;kEAET,gBAAgB,gCACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,WAAW;;;;;;;;;;;;kEAG5B,8OAAC;wDACC,SAAS,IACP,gBAAgB,YAAY,WAAW,EAAE;wDAE3C,WAAU;kEAET,gBAAgB,8BACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAItB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,IAAI;;;;;;;;;;;;kEAGrB,8OAAC;wDACC,SAAS,IAAM,gBAAgB,YAAY,IAAI,EAAE;wDACjD,WAAU;kEAET,gBAAgB,uBACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,SAAS;;;;;;;;;;;;kEAG1B,8OAAC;wDACC,SAAS,IACP,gBAAgB,YAAY,SAAS,EAAE;wDAEzC,WAAU;kEAET,gBAAgB,4BACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAMvB,MAAM,aAAa,WAAW,yBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,aAAa;;;;;;;;;;;;kEAG9B,8OAAC;wDACC,SAAS,IACP,gBACE,YAAY,aAAa,EACzB;wDAGJ,WAAU;kEAET,gBAAgB,gCACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,SAAS;;;;;;;;;;;;kEAG1B,8OAAC;wDACC,SAAS,IACP,gBAAgB,YAAY,SAAS,EAAE;wDAEzC,WAAU;kEAET,gBAAgB,4BACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,kBAAkB;;;;;;;;;;;;kEAGnC,8OAAC;wDACC,SAAS,IACP,gBACE,YAAY,kBAAkB,EAC9B;wDAGJ,WAAU;kEAET,gBAAgB,qCACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAE,WAAU;0EACV,YAAY,gBAAgB;;;;;;;;;;;;kEAGjC,8OAAC;wDACC,SAAS,IACP,gBACE,YAAY,gBAAgB,EAC5B;wDAGJ,WAAU;kEAET,gBAAgB,mCACf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;iFAEvB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU9B,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qIAAA,CAAA,UAAK;wCACJ,IAAG;wCACH,OAAM;wCACN,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,OAAO,CAAC,CAAC,OAAO,IAAI;wCACpB,cAAc,OAAO,IAAI;wCACzB,QAAQ;;;;;;kDAGV,8OAAC,qIAAA,CAAA,UAAK;wCACJ,IAAG;wCACH,OAAM;wCACN,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,MAAM;wCACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC3D,OAAO,CAAC,CAAC,OAAO,MAAM;wCACtB,cAAc,OAAO,MAAM;wCAC3B,QAAQ;;;;;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;oDAA0C;kEAEzD,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDACC,SAAQ;wDACR,WAAW,CAAC,yHAAyH,EACnI,OAAO,eAAe,GAClB,6BACA,gDACJ;kEAEF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAE,WAAU;8EACV,SAAS,eAAe,GACrB,SAAS,eAAe,CAAC,IAAI,GAC7B;;;;;;;;;;;;;;;;;;;;;;;4CAKX,OAAO,eAAe,kBACrB,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,eAAe;;;;;;;;;;;;;;;;;;4BAMhE,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA0C;;;;;;kDAG3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK;gDACL,KAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDACC,MAAK;gDACL,SAAS;oDACP,YAAY,CAAC,OAAS,CAAC;4DACrB,GAAG,IAAI;4DACP,iBAAiB;wDACnB,CAAC;oDACD,gBAAgB;gDAClB;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA0C;;;;;;kDAG3D,8OAAC;wCACC,IAAG;wCACH,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,aAAY;wCACZ,MAAM;wCACN,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}, {"offset": {"line": 4643, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/PriceRequestForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { toast } from \"react-toastify\";\nimport { Package, Loader2, Upload, X } from \"lucide-react\";\nimport Input from \"@/components/common/Input\";\nimport { PriceRequestFormData, PriceRequestFormErrors } from \"@/types/account\";\nimport { BACKEND_URL } from \"@/config\";\n\nconst PriceRequestForm: React.FC = () => {\n  const [formData, setFormData] = useState<PriceRequestFormData>({\n    product_name: \"\",\n    quantity: \"\",\n    notes: \"\",\n    images: [],\n  });\n  const [errors, setErrors] = useState<PriceRequestFormErrors>({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [imagePreviews, setImagePreviews] = useState<string[]>([]);\n  const [prefilledImageUrls, setPrefilledImageUrls] = useState<string[]>([]);\n  const formRef = useRef<HTMLDivElement>(null);\n\n  // Load pre-filled data from localStorage if available\n  useEffect(() => {\n    const savedProductData = localStorage.getItem(\"priceRequestProduct\");\n    if (savedProductData) {\n      try {\n        const productData = JSON.parse(savedProductData);\n        setFormData((prev) => ({\n          ...prev,\n          product_name: productData.product_name || \"\",\n          quantity: productData.quantity || \"\",\n          notes: productData.notes || \"\",\n          images: [], // Reset images array\n        }));\n\n        // If product has images, show them as previews\n        if (productData.images && productData.images.length > 0) {\n          setPrefilledImageUrls(productData.images);\n          setImagePreviews(\n            productData.images.map((img: string) => `${BACKEND_URL}${img}`)\n          );\n        }\n\n        // Clear the saved data\n        localStorage.removeItem(\"priceRequestProduct\");\n\n        // Scroll to form after a short delay to ensure rendering is complete\n        setTimeout(() => {\n          if (formRef.current) {\n            formRef.current.scrollIntoView({\n              behavior: \"smooth\",\n              block: \"start\",\n            });\n          }\n        }, 100);\n      } catch (error) {\n        console.error(\"Error parsing saved product data:\", error);\n      }\n    }\n  }, []);\n\n  const validateForm = (): boolean => {\n    const newErrors: PriceRequestFormErrors = {};\n\n    if (!formData.product_name.trim()) {\n      newErrors.product_name = \"اسم المنتج مطلوب\";\n    } else if (formData.product_name.trim().length < 2) {\n      newErrors.product_name = \"اسم المنتج قصير جداً\";\n    } else if (formData.product_name.trim().length > 200) {\n      newErrors.product_name = \"اسم المنتج طويل جداً\";\n    }\n\n    if (!String(formData.quantity).trim()) {\n      newErrors.quantity = \"الكمية مطلوبة\";\n    } else if (\n      isNaN(Number(formData.quantity)) ||\n      Number(formData.quantity) <= 0\n    ) {\n      newErrors.quantity = \"يرجى إدخال كمية صحيحة\";\n    }\n\n    if (formData.notes.trim().length > 1000) {\n      newErrors.notes = \"الملاحظات طويلة جداً (حد أقصى 1000 حرف)\";\n    }\n\n    const totalImages = formData.images.length + prefilledImageUrls.length;\n    if (totalImages > 5) {\n      newErrors.images = \"يمكن رفع حد أقصى 5 صور\";\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleInputChange = (\n    field: keyof PriceRequestFormData,\n    value: string\n  ) => {\n    setFormData((prev) => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || []);\n\n    if (files.length === 0) return;\n\n    // Validate total number of images (including prefilled ones)\n    const totalImages =\n      formData.images.length + prefilledImageUrls.length + files.length;\n    if (totalImages > 5) {\n      setErrors((prev) => ({\n        ...prev,\n        images: \"يمكن رفع حد أقصى 5 صور\",\n      }));\n      return;\n    }\n\n    // Validate each file\n    const validFiles: File[] = [];\n    const newPreviews: string[] = [];\n\n    for (const file of files) {\n      // Validate file type\n      if (!file.type.startsWith(\"image/\")) {\n        setErrors((prev) => ({\n          ...prev,\n          images: \"يرجى اختيار ملفات صور صحيحة فقط\",\n        }));\n        continue;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setErrors((prev) => ({\n          ...prev,\n          images: \"حجم كل صورة يجب أن يكون أقل من 5 ميجابايت\",\n        }));\n        continue;\n      }\n\n      validFiles.push(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        newPreviews.push(e.target?.result as string);\n        if (newPreviews.length === validFiles.length) {\n          setImagePreviews((prev) => [...prev, ...newPreviews]);\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n\n    if (validFiles.length > 0) {\n      setFormData((prev) => ({\n        ...prev,\n        images: [...prev.images, ...validFiles],\n      }));\n      setErrors((prev) => ({ ...prev, images: undefined }));\n    }\n  };\n\n  const removeImage = (index: number) => {\n    // Check if it's a prefilled image or a new uploaded image\n    if (index < prefilledImageUrls.length) {\n      // Remove prefilled image\n      setPrefilledImageUrls((prev) => prev.filter((_, i) => i !== index));\n      setImagePreviews((prev) => prev.filter((_, i) => i !== index));\n    } else {\n      // Remove uploaded image\n      const actualIndex = index - prefilledImageUrls.length;\n      setFormData((prev) => ({\n        ...prev,\n        images: prev.images.filter((_, i) => i !== actualIndex),\n      }));\n      setImagePreviews((prev) => prev.filter((_, i) => i !== index));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log(formData);\n    if (!validateForm()) {\n      toast.error(\"يرجى تصحيح الأخطاء في النموذج\");\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Create FormData for file upload\n      const submitData = new FormData();\n      submitData.append(\"product_name\", formData.product_name);\n      submitData.append(\"quantity\", formData.quantity);\n      submitData.append(\"notes\", formData.notes);\n\n      // Append new uploaded images\n      formData.images.forEach((image) => {\n        submitData.append(\"images\", image);\n      });\n\n      // Append prefilled image URLs as references\n      prefilledImageUrls.forEach((imageUrl) => {\n        submitData.append(\"existing_images\", imageUrl);\n      });\n\n      // Submit to API\n      const response = await fetch(\n        \"https://api.imdadport.com/api/price/request/\",\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n          body: submitData,\n        }\n      );\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || \"فشل في إرسال طلب السعر\");\n      }\n\n      toast.success(\"تم إرسال طلب السعر بنجاح\", {\n        autoClose: 3000,\n        hideProgressBar: true,\n      });\n\n      // Reset form\n      setFormData({\n        product_name: \"\",\n        quantity: \"\",\n        notes: \"\",\n        images: [],\n      });\n      setImagePreviews([]);\n      setPrefilledImageUrls([]);\n\n      // Redirect to price requests list\n      window.location.href = \"/account?tab=price-requests\";\n    } catch (error) {\n      console.error(\"Error submitting price request:\", error);\n      toast.error(\"فشل في إرسال طلب السعر\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div\n      ref={formRef}\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white flex items-center\">\n          <Package className=\"w-5 h-5 ml-2 text-yellow-400\" />\n          طلب سعر منتج\n        </h3>\n        <p className=\"text-sm text-gray-300 mt-1\">أرسل طلب تسعير لمنتج جديد</p>\n      </div>\n\n      <div className=\"p-8\">\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <Input\n              id=\"product_name\"\n              label=\"اسم المنتج\"\n              type=\"text\"\n              placeholder=\"مثال: قمح أحمر ناعم\"\n              value={formData.product_name}\n              onChange={(e) =>\n                handleInputChange(\"product_name\", e.target.value)\n              }\n              error={!!errors.product_name}\n              errorMessage={errors.product_name}\n              required\n            />\n\n            <Input\n              id=\"quantity\"\n              label=\"الكمية\"\n              type=\"number\"\n              placeholder=\"مثال: 1000\"\n              value={formData.quantity}\n              onChange={(e) => handleInputChange(\"quantity\", e.target.value)}\n              error={!!errors.quantity}\n              errorMessage={errors.quantity}\n              required\n            />\n          </div>\n\n          {/* Image Upload Section */}\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              صور المنتج (اختياري)\n              <span className=\"text-gray-500 text-xs mr-2\">\n                حد أقصى 5 صور (\n                {formData.images.length + prefilledImageUrls.length}/5)\n              </span>\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"file\"\n                id=\"productImages\"\n                accept=\"image/*\"\n                multiple\n                onChange={handleImageChange}\n                className=\"hidden\"\n              />\n              <label\n                htmlFor=\"productImages\"\n                className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg transition-colors cursor-pointer ${\n                  errors.images\n                    ? \"border-red-300 bg-red-50\"\n                    : \"border-gray-300 bg-gray-50 hover:bg-gray-100\"\n                }`}\n              >\n                <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                  <Upload className=\"w-8 h-8 mb-2 text-gray-500\" />\n                  <p className=\"text-sm text-gray-500\">\n                    اختر صور المنتج أو اسحبها هنا\n                  </p>\n                  <p className=\"text-xs text-gray-400\">\n                    PNG, JPG, GIF حتى 5MB لكل صورة\n                  </p>\n                </div>\n              </label>\n            </div>\n            {errors.images && (\n              <p className=\"text-sm text-red-600\">{errors.images}</p>\n            )}\n          </div>\n\n          {/* Image Previews */}\n          {imagePreviews.length > 0 && (\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                معاينة الصور ({imagePreviews.length})\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\">\n                {imagePreviews.map((preview, index) => (\n                  <div key={preview} className=\"relative group\">\n                    <img\n                      src={preview}\n                      alt=\"معاينة\"\n                      className=\"w-full h-24 object-cover rounded-lg border border-gray-300\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => removeImage(index)}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors cursor-pointer\"\n                    >\n                      <X className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"space-y-2\">\n            <label\n              htmlFor=\"notes\"\n              className=\"block text-sm font-medium text-gray-700\"\n            >\n              ملاحظات (اختياري)\n            </label>\n            <textarea\n              id=\"notes\"\n              value={formData.notes}\n              onChange={(e) => handleInputChange(\"notes\", e.target.value)}\n              placeholder=\"أي تفاصيل إضافية حول المنتج أو المتطلبات الخاصة...\"\n              className={`w-full min-h-[120px] px-4 py-2 border rounded-lg focus:ring-2 focus:ring-gray-200 focus:border-gray-400 transition-colors resize-y ${\n                errors.notes ? \"border-red-500\" : \"border-gray-300\"\n              }`}\n            />\n            {errors.notes && (\n              <p className=\"text-sm text-red-600\">{errors.notes}</p>\n            )}\n          </div>\n\n          <div className=\"pt-8\">\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] cursor-pointer\"\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"w-5 h-5 animate-spin ml-2\" />\n                  جاري الإرسال...\n                </>\n              ) : (\n                <>\n                  <Package className=\"w-5 h-5 ml-2\" />\n                  إرسال طلب السعر\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default PriceRequestForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,mBAA6B;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QAC7D,cAAc;QACd,UAAU;QACV,OAAO;QACP,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,IAAI,kBAAkB;YACpB,IAAI;gBACF,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,YAAY,CAAC,OAAS,CAAC;wBACrB,GAAG,IAAI;wBACP,cAAc,YAAY,YAAY,IAAI;wBAC1C,UAAU,YAAY,QAAQ,IAAI;wBAClC,OAAO,YAAY,KAAK,IAAI;wBAC5B,QAAQ,EAAE;oBACZ,CAAC;gBAED,+CAA+C;gBAC/C,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,MAAM,GAAG,GAAG;oBACvD,sBAAsB,YAAY,MAAM;oBACxC,iBACE,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,MAAgB,GAAG,6GAAA,CAAA,cAAW,GAAG,KAAK;gBAElE;gBAEA,uBAAuB;gBACvB,aAAa,UAAU,CAAC;gBAExB,qEAAqE;gBACrE,WAAW;oBACT,IAAI,QAAQ,OAAO,EAAE;wBACnB,QAAQ,OAAO,CAAC,cAAc,CAAC;4BAC7B,UAAU;4BACV,OAAO;wBACT;oBACF;gBACF,GAAG;YACL,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,UAAU,YAAY,GAAG;QAC3B,OAAO,IAAI,SAAS,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAClD,UAAU,YAAY,GAAG;QAC3B,OAAO,IAAI,SAAS,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;YACpD,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,CAAC,OAAO,SAAS,QAAQ,EAAE,IAAI,IAAI;YACrC,UAAU,QAAQ,GAAG;QACvB,OAAO,IACL,MAAM,OAAO,SAAS,QAAQ,MAC9B,OAAO,SAAS,QAAQ,KAAK,GAC7B;YACA,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM;YACvC,UAAU,KAAK,GAAG;QACpB;QAEA,MAAM,cAAc,SAAS,MAAM,CAAC,MAAM,GAAG,mBAAmB,MAAM;QACtE,IAAI,cAAc,GAAG;YACnB,UAAU,MAAM,GAAG;QACrB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CACxB,OACA;QAEA,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAE7C,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,6DAA6D;QAC7D,MAAM,cACJ,SAAS,MAAM,CAAC,MAAM,GAAG,mBAAmB,MAAM,GAAG,MAAM,MAAM;QACnE,IAAI,cAAc,GAAG;YACnB,UAAU,CAAC,OAAS,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;gBACV,CAAC;YACD;QACF;QAEA,qBAAqB;QACrB,MAAM,aAAqB,EAAE;QAC7B,MAAM,cAAwB,EAAE;QAEhC,KAAK,MAAM,QAAQ,MAAO;YACxB,qBAAqB;YACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,UAAU,CAAC,OAAS,CAAC;wBACnB,GAAG,IAAI;wBACP,QAAQ;oBACV,CAAC;gBACD;YACF;YAEA,+BAA+B;YAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,UAAU,CAAC,OAAS,CAAC;wBACnB,GAAG,IAAI;wBACP,QAAQ;oBACV,CAAC;gBACD;YACF;YAEA,WAAW,IAAI,CAAC;YAEhB,iBAAiB;YACjB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,YAAY,IAAI,CAAC,EAAE,MAAM,EAAE;gBAC3B,IAAI,YAAY,MAAM,KAAK,WAAW,MAAM,EAAE;oBAC5C,iBAAiB,CAAC,OAAS;+BAAI;+BAAS;yBAAY;gBACtD;YACF;YACA,OAAO,aAAa,CAAC;QACvB;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;2BAAK;qBAAW;gBACzC,CAAC;YACD,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,CAAC;QACrD;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,0DAA0D;QAC1D,IAAI,QAAQ,mBAAmB,MAAM,EAAE;YACrC,yBAAyB;YACzB,sBAAsB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC5D,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACzD,OAAO;YACL,wBAAwB;YACxB,MAAM,cAAc,QAAQ,mBAAmB,MAAM;YACrD,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC7C,CAAC;YACD,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACzD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,gBAAgB;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,kCAAkC;YAClC,MAAM,aAAa,IAAI;YACvB,WAAW,MAAM,CAAC,gBAAgB,SAAS,YAAY;YACvD,WAAW,MAAM,CAAC,YAAY,SAAS,QAAQ;YAC/C,WAAW,MAAM,CAAC,SAAS,SAAS,KAAK;YAEzC,6BAA6B;YAC7B,SAAS,MAAM,CAAC,OAAO,CAAC,CAAC;gBACvB,WAAW,MAAM,CAAC,UAAU;YAC9B;YAEA,4CAA4C;YAC5C,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,WAAW,MAAM,CAAC,mBAAmB;YACvC;YAEA,gBAAgB;YAChB,MAAM,WAAW,MAAM,MACrB,gDACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;gBACA,MAAM;YACR;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI;YACvC;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,4BAA4B;gBACxC,WAAW;gBACX,iBAAiB;YACnB;YAEA,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,UAAU;gBACV,OAAO;gBACP,QAAQ,EAAE;YACZ;YACA,iBAAiB,EAAE;YACnB,sBAAsB,EAAE;YAExB,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,UAAK;oCACJ,IAAG;oCACH,OAAM;oCACN,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IACT,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAElD,OAAO,CAAC,CAAC,OAAO,YAAY;oCAC5B,cAAc,OAAO,YAAY;oCACjC,QAAQ;;;;;;8CAGV,8OAAC,qIAAA,CAAA,UAAK;oCACJ,IAAG;oCACH,OAAM;oCACN,MAAK;oCACL,aAAY;oCACZ,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC7D,OAAO,CAAC,CAAC,OAAO,QAAQ;oCACxB,cAAc,OAAO,QAAQ;oCAC7B,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCAA0C;sDAEzD,8OAAC;4CAAK,WAAU;;gDAA6B;gDAE1C,SAAS,MAAM,CAAC,MAAM,GAAG,mBAAmB,MAAM;gDAAC;;;;;;;;;;;;;8CAGxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,QAAO;4CACP,QAAQ;4CACR,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAQ;4CACR,WAAW,CAAC,yHAAyH,EACnI,OAAO,MAAM,GACT,6BACA,gDACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;kEAGrC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;gCAM1C,OAAO,MAAM,kBACZ,8OAAC;oCAAE,WAAU;8CAAwB,OAAO,MAAM;;;;;;;;;;;;wBAKrD,cAAc,MAAM,GAAG,mBACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCAA0C;wCAC1C,cAAc,MAAM;wCAAC;;;;;;;8CAEtC,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;8DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;2CAXP;;;;;;;;;;;;;;;;sCAmBlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,aAAY;oCACZ,WAAW,CAAC,mIAAmI,EAC7I,OAAO,KAAK,GAAG,mBAAmB,mBAClC;;;;;;gCAEH,OAAO,KAAK,kBACX,8OAAC;oCAAE,WAAU;8CAAwB,OAAO,KAAK;;;;;;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD;uCAEe", "debugId": null}}, {"offset": {"line": 5223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/currencyUtils.ts"], "sourcesContent": ["export interface CurrencyInfo {\r\n  code: string;\r\n  name: string;\r\n  symbol: string;\r\n  locale?: string;\r\n}\r\n\r\nexport const CURRENCY_OPTIONS: CurrencyInfo[] = [\r\n  { code: 'USD', name: 'الدولار الأمريكي', symbol: '$', locale: 'en-US' },\r\n  { code: 'EUR', name: 'اليورو', symbol: '€', locale: 'de-DE' },\r\n  { code: 'SAR', name: 'الريال السعودي', symbol: 'ر.س', locale: 'ar-SA' },\r\n  { code: 'AED', name: 'الدرهم الإماراتي', symbol: 'د.إ', locale: 'ar-AE' },\r\n  { code: 'EGP', name: 'الجنيه المصري', symbol: 'ج.م', locale: 'ar-EG' },\r\n  { code: 'KWD', name: 'الدينار الكويتي', symbol: 'د.ك', locale: 'ar-KW' },\r\n  { code: 'QAR', name: 'الريال القطري', symbol: 'ر.ق', locale: 'ar-QA' },\r\n  { code: 'BHD', name: 'الدينار البحريني', symbol: 'د.ب', locale: 'ar-BH' },\r\n  { code: 'OMR', name: 'الريال العماني', symbol: 'ر.ع', locale: 'ar-OM' },\r\n  { code: 'JOD', name: 'الدينار الأردني', symbol: 'د.أ', locale: 'ar-JO' },\r\n  { code: 'LBP', name: 'الليرة اللبنانية', symbol: 'ل.ل', locale: 'ar-LB' },\r\n  { code: 'IQD', name: 'الدينار العراقي', symbol: 'ع.د', locale: 'ar-IQ' },\r\n  { code: 'SYP', name: 'الليرة السورية', symbol: 'ل.س', locale: 'ar-SY' },\r\n\r\n  { code: 'YER', name: 'الريال اليمني', symbol: 'ر.ي', locale: 'ar-YE' },\r\n  { code: 'SDG', name: 'الجنيه السوداني', symbol: 'ج.س', locale: 'ar-SD' },\r\n  { code: 'MAD', name: 'الدرهم المغربي', symbol: 'د.م', locale: 'ar-MA' },\r\n  { code: 'DZD', name: 'الدينار الجزائري', symbol: 'د.ج', locale: 'ar-DZ' },\r\n  { code: 'TND', name: 'الدينار التونسي', symbol: 'د.ت', locale: 'ar-TN' },\r\n  { code: 'LYD', name: 'الدينار الليبي', symbol: 'ل.د', locale: 'ar-LY' },\r\n  { code: 'MRU', name: 'الأوقية الموريتانية', symbol: 'أ.م', locale: 'ar-MR' },\r\n  { code: 'SOS', name: 'الشلن الصومالي', symbol: 'S', locale: 'so-SO' },\r\n  { code: 'DJF', name: 'الفرنك الجيبوتي', symbol: 'Fdj', locale: 'fr-DJ' },\r\n  { code: 'KMF', name: 'الفرنك القمري', symbol: 'CF', locale: 'fr-KM' },\r\n  // Other major currencies for completeness\r\n  { code: 'GBP', name: 'الجنيه الإسترليني', symbol: '£', locale: 'en-GB' },\r\n  { code: 'JPY', name: 'الين الياباني', symbol: '¥', locale: 'ja-JP' },\r\n  { code: 'CNY', name: 'اليوان الصيني', symbol: '¥', locale: 'zh-CN' },\r\n  { code: 'INR', name: 'الروبية الهندية', symbol: '₹', locale: 'en-IN' },\r\n];\r\n\r\nexport const getCurrencyInfo = (currencyCode: string): CurrencyInfo => {\r\n  return CURRENCY_OPTIONS.find(currency => currency.code === currencyCode) || CURRENCY_OPTIONS[0];\r\n};\r\n\r\nexport const formatCurrency = (amount: number, currencyCode: string = 'USD'): string => {\r\n  const currencyInfo = getCurrencyInfo(currencyCode);\r\n  \r\n  try {\r\n    return new Intl.NumberFormat(currencyInfo.locale || 'en-US', {\r\n      style: 'currency',\r\n      currency: currencyCode,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Fallback formatting\r\n    return `${currencyInfo.symbol}${amount.toLocaleString()}`;\r\n  }\r\n};\r\n\r\nexport const formatCurrencySimple = (amount: number, currencyCode: string = 'USD'): string => {\r\n  const currencyInfo = getCurrencyInfo(currencyCode);\r\n  return `${currencyInfo.symbol}${amount.toLocaleString()}`;\r\n};\r\n\r\nexport const getUserCurrency = (): string => {\r\n  // Try to get from localStorage first (for guest users)\r\n  const storedCurrency = localStorage.getItem('userCurrency');\r\n  if (storedCurrency) {\r\n    return storedCurrency;\r\n  }\r\n  \r\n  // Default to USD\r\n  return 'USD';\r\n};\r\n\r\nexport const setUserCurrency = (currencyCode: string): void => {\r\n  localStorage.setItem('userCurrency', currencyCode);\r\n}; "], "names": [], "mappings": ";;;;;;;;AAOO,MAAM,mBAAmC;IAC9C;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAK,QAAQ;IAAQ;IACtE;QAAE,MAAM;QAAO,MAAM;QAAU,QAAQ;QAAK,QAAQ;IAAQ;IAC5D;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAO,QAAQ;IAAQ;IACtE;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAO,QAAQ;IAAQ;IACxE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAO,QAAQ;IAAQ;IACrE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAO,QAAQ;IAAQ;IACrE;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAO,QAAQ;IAAQ;IACxE;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAO,QAAQ;IAAQ;IACtE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAO,QAAQ;IAAQ;IACxE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAO,QAAQ;IAAQ;IAEtE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAO,QAAQ;IAAQ;IACrE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAO,QAAQ;IAAQ;IACtE;QAAE,MAAM;QAAO,MAAM;QAAoB,QAAQ;QAAO,QAAQ;IAAQ;IACxE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAO,QAAQ;IAAQ;IACtE;QAAE,MAAM;QAAO,MAAM;QAAuB,QAAQ;QAAO,QAAQ;IAAQ;IAC3E;QAAE,MAAM;QAAO,MAAM;QAAkB,QAAQ;QAAK,QAAQ;IAAQ;IACpE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAO,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAM,QAAQ;IAAQ;IACpE,0CAA0C;IAC1C;QAAE,MAAM;QAAO,MAAM;QAAqB,QAAQ;QAAK,QAAQ;IAAQ;IACvE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAK,QAAQ;IAAQ;IACnE;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;QAAK,QAAQ;IAAQ;IACnE;QAAE,MAAM;QAAO,MAAM;QAAmB,QAAQ;QAAK,QAAQ;IAAQ;CACtE;AAEM,MAAM,kBAAkB,CAAC;IAC9B,OAAO,iBAAiB,IAAI,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK,iBAAiB,gBAAgB,CAAC,EAAE;AACjG;AAEO,MAAM,iBAAiB,CAAC,QAAgB,eAAuB,KAAK;IACzE,MAAM,eAAe,gBAAgB;IAErC,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,aAAa,MAAM,IAAI,SAAS;YAC3D,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,sBAAsB;QACtB,OAAO,GAAG,aAAa,MAAM,GAAG,OAAO,cAAc,IAAI;IAC3D;AACF;AAEO,MAAM,uBAAuB,CAAC,QAAgB,eAAuB,KAAK;IAC/E,MAAM,eAAe,gBAAgB;IACrC,OAAO,GAAG,aAAa,MAAM,GAAG,OAAO,cAAc,IAAI;AAC3D;AAEO,MAAM,kBAAkB;IAC7B,uDAAuD;IACvD,MAAM,iBAAiB,aAAa,OAAO,CAAC;IAC5C,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,iBAAiB;IACjB,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC;IAC9B,aAAa,OAAO,CAAC,gBAAgB;AACvC", "debugId": null}}, {"offset": {"line": 5435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/PriceRequestsManagement.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  Package,\n  Trash2,\n  Eye,\n  Calendar,\n  Image as ImageIcon,\n  XCircle,\n} from \"lucide-react\";\nimport { PriceRequest as PriceRequestType } from \"@/types/account\";\nimport Image from \"next/image\";\nimport { BACKEND_URL } from \"@/config\";\nimport { formatCurrency } from \"@/utils/currencyUtils\";\n\ninterface PriceRequestFile {\n  id: number;\n  file: string;\n  uploaded_at: string;\n}\n\ntype PriceRequest = Omit<PriceRequestType, \"status\"> & {\n  status: \"pending\" | \"quoted\" | \"rejected\" | \"paid\";\n  user_currency: string;\n  files?: PriceRequestFile[];\n};\n\nconst PriceRequestsManagement: React.FC = () => {\n  const [priceRequests, setPriceRequests] = useState<PriceRequest[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedRequest, setSelectedRequest] = useState<PriceRequest | null>(\n    null\n  );\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [requestToDelete, setRequestToDelete] = useState<PriceRequest | null>(\n    null\n  );\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [requestToPay, setRequestToPay] = useState<PriceRequest | null>(null);\n  const [showImageModal, setShowImageModal] = useState(false);\n  const [selectedImage, setSelectedImage] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchPriceRequests();\n  }, []);\n\n  const fetchPriceRequests = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        \"https://api.imdadport.com/api/price/my-requests/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setPriceRequests(data);\n      } else {\n        toast.error(\"فشل في تحميل طلبات الأسعار\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching price requests:\", error);\n      toast.error(\"فشل في تحميل طلبات الأسعار\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (requestId: number) => {\n    try {\n      const response = await fetch(\n        `https://api.imdadport.com/api/price/requests/${requestId}/delete/`,\n        {\n          method: \"DELETE\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        toast.success(\"تم حذف الطلب بنجاح\");\n        fetchPriceRequests();\n      } else {\n        toast.error(\"فشل في حذف الطلب\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting price request:\", error);\n      toast.error(\"فشل في حذف الطلب\");\n    }\n  };\n\n  const handleDeleteClick = (request: PriceRequest) => {\n    setRequestToDelete(request);\n    setShowDeleteModal(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    if (requestToDelete) {\n      await handleDelete(requestToDelete.id);\n      setShowDeleteModal(false);\n      setRequestToDelete(null);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"pending\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n            قيد المراجعة\n          </span>\n        );\n      case \"quoted\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n            تم التسعير\n          </span>\n        );\n      case \"rejected\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n            مرفوض\n          </span>\n        );\n      case \"paid\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n            مدفوع\n          </span>\n        );\n      default:\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            غير محدد\n          </span>\n        );\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const handlePay = async (request: PriceRequest) => {\n    if (!request.quoted_price) return;\n    setRequestToPay(request);\n    setShowPaymentModal(true);\n  };\n\n  const handlePaymentConfirm = async () => {\n    if (!requestToPay || typeof requestToPay.id !== \"number\") return;\n\n    const quotedPrice = requestToPay.quoted_price;\n    if (quotedPrice === undefined || quotedPrice === null) {\n      toast.error(\"لم يتم تحديد سعر للطلب بعد\");\n      return;\n    }\n\n    try {\n      const formData = new FormData();\n      formData.append(\"amount\", quotedPrice.toString());\n\n      const response = await fetch(\n        `https://api.imdadport.com/api/price/requests/${requestToPay.id!}/pay/`,\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n          body: formData,\n        }\n      );\n\n      if (response.ok) {\n        toast.success(\"تم الدفع بنجاح\");\n        fetchPriceRequests();\n      } else {\n        const data = await response.json();\n        toast.error(data.message || \"فشل في عملية الدفع\");\n      }\n    } catch {\n      toast.error(\"حدث خطأ أثناء عملية الدفع\");\n    } finally {\n      setShowPaymentModal(false);\n      setRequestToPay(null);\n    }\n  };\n\n  const handleReuseRequest = (request: PriceRequest) => {\n    // Save request data to localStorage\n    localStorage.setItem(\n      \"priceRequestProduct\",\n      JSON.stringify({\n        product_name: request.product_name,\n        quantity: request.quantity,\n        notes: request.notes,\n        images: request.images?.map((img) => img.image) || [],\n      })\n    );\n\n    // Save the active tab to localStorage and redirect to account page\n    localStorage.setItem(\"userAccountActiveTab\", \"price-request\");\n    window.location.href = \"/account\";\n  };\n\n  const formatPrice = (price: number | undefined, currency: string = \"USD\") => {\n    if (!price) return \"غير محدد\";\n    return formatCurrency(price, currency);\n  };\n\n  if (loading) {\n    return (\n      <div\n        className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n        dir=\"rtl\"\n      >\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <h3 className=\"text-xl font-bold text-white\">طلبات الأسعار</h3>\n        </div>\n        <div className=\"p-8 flex justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n      dir=\"rtl\"\n    >\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n        <h3 className=\"text-xl font-bold text-white\">طلبات الأسعار</h3>\n        <p className=\"text-sm text-gray-300 mt-1\">\n          إدارة وتتبع طلبات الأسعار الخاصة بك\n        </p>\n      </div>\n\n      <div className=\"p-6\">\n        {priceRequests.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Package className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">\n              لا توجد طلبات أسعار\n            </h3>\n            <p className=\"text-gray-500\">لم تقم بإرسال أي طلبات أسعار بعد</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {priceRequests.map((request) => (\n              <div\n                key={request.id}\n                className=\"border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-2\">\n                      <h4 className=\"text-lg font-semibold text-gray-900\">\n                        {request.product_name}\n                      </h4>\n                      {getStatusBadge(request.status)}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3\">\n                      <div className=\"flex items-center\">\n                        <Package className=\"w-4 h-4 ml-2\" />\n                        <span>الكمية: {request.quantity}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Calendar className=\"w-4 h-4 ml-2\" />\n                        <span>\n                          تاريخ الطلب: {formatDate(request.created_at)}\n                        </span>\n                      </div>\n                      {request.quoted_price && (\n                        <div className=\"flex items-center\">\n                          <span className=\"font-semibold text-green-600\">\n                            السعر المقترح:{\" \"}\n                            {formatPrice(\n                              request.quoted_price,\n                              request.user_currency\n                            )}\n                          </span>\n                        </div>\n                      )}\n                      {request.images.length > 0 && (\n                        <div className=\"flex items-center\">\n                          <ImageIcon className=\"w-4 h-4 ml-2\" />\n                          <span>{request.images.length} صورة</span>\n                        </div>\n                      )}\n                    </div>\n\n                    {request.notes && (\n                      <div className=\"mb-3\">\n                        <p className=\"text-sm text-gray-600 bg-gray-50 p-2 rounded\">\n                          <span className=\"font-medium\">الملاحظات: </span>\n                          {request.notes}\n                        </p>\n                      </div>\n                    )}\n\n                    {request.files && request.files.length > 0 && (\n                      <div className=\"mb-3\">\n                        <p className=\"text-sm text-gray-700 font-medium mb-1\">\n                          الملفات المرفقة:\n                        </p>\n                        <ul className=\"list-disc pr-4\">\n                          {request.files.map((file) => (\n                            <li key={file.id} className=\"mb-1\">\n                              <a\n                                href={`${BACKEND_URL}${file.file}`}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"text-blue-600 hover:underline\"\n                              >\n                                {file.file.split(\"/\").pop()}\n                              </a>\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n\n                    {request.admin_notes && (\n                      <div className=\"mb-3\">\n                        <p className=\"text-sm text-blue-600 bg-blue-50 p-2 rounded\">\n                          <span className=\"font-medium\">ملاحظات الإدارة: </span>\n                          {request.admin_notes}\n                        </p>\n                      </div>\n                    )}\n\n                    {request.status === \"quoted\" && request.quoted_price && (\n                      <div className=\"mt-4 flex items-center gap-4\">\n                        <button\n                          onClick={() => handlePay(request)}\n                          className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                        >\n                          دفع{\" \"}\n                          {formatPrice(\n                            request.quoted_price,\n                            request.user_currency\n                          )}\n                        </button>\n                      </div>\n                    )}\n\n                    {request.status === \"paid\" && (\n                      <div className=\"mt-4\">\n                        <button\n                          onClick={() => handleReuseRequest(request)}\n                          className=\"text-blue-600 hover:text-blue-800 flex items-center gap-2\"\n                        >\n                          <Package className=\"w-4 h-4\" />\n                          إعادة استخدام الطلب\n                        </button>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center gap-2 mr-4\">\n                    <button\n                      onClick={() => {\n                        setSelectedRequest(request);\n                        setShowViewModal(true);\n                      }}\n                      className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors cursor-pointer\"\n                      title=\"عرض التفاصيل\"\n                    >\n                      <Eye className=\"w-4 h-4\" />\n                    </button>\n\n                    {request.status === \"pending\" && (\n                      <button\n                        onClick={() => handleDeleteClick(request)}\n                        className=\"p-2 text-red-600 hover:text-red-900 hover:bg-red-100 rounded-lg transition-colors cursor-pointer\"\n                        title=\"حذف\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* View Modal */}\n      {showViewModal && selectedRequest && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">تفاصيل طلب السعر</h3>\n            </div>\n\n            <div className=\"p-6 space-y-4\">\n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">اسم المنتج</h4>\n                <p className=\"text-gray-600\">{selectedRequest.product_name}</p>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">الكمية</h4>\n                  <p className=\"text-gray-600\">{selectedRequest.quantity}</p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">الحالة</h4>\n                  {getStatusBadge(selectedRequest.status)}\n                </div>\n              </div>\n\n              {selectedRequest.quoted_price && (\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    السعر المقترح\n                  </h4>\n                  <p className=\"text-green-600 font-semibold\">\n                    {selectedRequest.quoted_price\n                      ? formatPrice(\n                          selectedRequest.quoted_price,\n                          selectedRequest.user_currency\n                        )\n                      : \"غير محدد\"}\n                  </p>\n                </div>\n              )}\n\n              {selectedRequest.notes && (\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    الملاحظات\n                  </h4>\n                  <p className=\"text-gray-600 bg-gray-50 p-3 rounded\">\n                    {selectedRequest.notes}\n                  </p>\n                </div>\n              )}\n\n              {selectedRequest.files && selectedRequest.files.length > 0 && (\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    الملفات المرفقة\n                  </h4>\n                  <ul className=\"list-disc pr-4\">\n                    {selectedRequest.files.map((file) => (\n                      <li key={file.id} className=\"mb-1\">\n                        <a\n                          href={`${BACKEND_URL}${file.file}`}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-blue-600 hover:underline\"\n                        >\n                          {file.file.split(\"/\").pop()}\n                        </a>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n\n              {selectedRequest.admin_notes && (\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    ملاحظات الإدارة\n                  </h4>\n                  <p className=\"text-blue-600 bg-blue-50 p-3 rounded\">\n                    {selectedRequest.admin_notes}\n                  </p>\n                </div>\n              )}\n\n              {selectedRequest.images.length > 0 && (\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">الصور</h4>\n                  <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                    {selectedRequest.images.map((image, index) => (\n                      <div\n                        key={index}\n                        className=\"relative h-24 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity\"\n                        onClick={() => {\n                          setSelectedImage(image.image);\n                          setShowImageModal(true);\n                        }}\n                      >\n                        <Image\n                          src={`${BACKEND_URL}${image.image}`}\n                          alt={`صورة ${index + 1}`}\n                          fill\n                          className=\"object-cover\"\n                        />\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              <div className=\"flex justify-end pt-4\">\n                <button\n                  onClick={() => setShowViewModal(false)}\n                  className=\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors cursor-pointer\"\n                >\n                  إغلاق\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteModal && requestToDelete && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl max-w-md w-full\">\n            <div className=\"bg-gradient-to-r from-red-900 to-red-800 px-6 py-4 rounded-t-2xl\">\n              <h3 className=\"text-xl font-bold text-white\">تأكيد الحذف</h3>\n            </div>\n\n            <div className=\"p-6\">\n              <p className=\"text-gray-600 mb-6\">\n                هل أنت متأكد من حذف طلب السعر &quot;\n                {requestToDelete.product_name}&quot;؟ لا يمكن التراجع عن هذا\n                الإجراء.\n              </p>\n\n              <div className=\"flex justify-end space-x-4 gap-4 space-x-reverse\">\n                <button\n                  onClick={() => {\n                    setShowDeleteModal(false);\n                    setRequestToDelete(null);\n                  }}\n                  className=\"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\"\n                >\n                  إلغاء\n                </button>\n                <button\n                  onClick={handleDeleteConfirm}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n                >\n                  حذف\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Payment Confirmation Modal */}\n      {showPaymentModal && requestToPay && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl max-w-md w-full\">\n            <div className=\"bg-gradient-to-r from-green-900 to-green-800 px-6 py-4 rounded-t-2xl\">\n              <h3 className=\"text-xl font-bold text-white\">تأكيد الدفع</h3>\n            </div>\n\n            <div className=\"p-6\">\n              <p className=\"text-gray-600 mb-6\">\n                هل أنت متأكد من دفع المبلغ{\" \"}\n                {formatPrice(\n                  requestToPay.quoted_price,\n                  requestToPay.user_currency\n                )}{\" \"}\n                لطلب السعر &quot;\n                {requestToPay.product_name}&quot;؟\n              </p>\n\n              <div className=\"flex justify-end space-x-4 gap-4 space-x-reverse\">\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setRequestToPay(null);\n                  }}\n                  className=\"px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\"\n                >\n                  إلغاء\n                </button>\n                <button\n                  onClick={() => {\n                    if (requestToPay && typeof requestToPay.id === \"number\") {\n                      handlePaymentConfirm();\n                    }\n                  }}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  تأكيد الدفع\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Modal */}\n      {showImageModal && selectedImage && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[60] p-4\"\n          onClick={() => setShowImageModal(false)}\n        >\n          <div className=\"relative max-w-[90vw] max-h-[90vh]\">\n            <img\n              src={`${BACKEND_URL}${selectedImage}`}\n              alt=\"صورة المنتج\"\n              className=\"max-w-full max-h-[90vh] object-contain rounded-lg\"\n            />\n            <button\n              onClick={() => setShowImageModal(false)}\n              className=\"absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 transition-colors\"\n            >\n              <XCircle className=\"w-6 h-6 text-gray-600\" />\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PriceRequestsManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAfA;;;;;;;;AA6BA,MAAM,0BAAoC;IACxC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,oDACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB;YACnB,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,6CAA6C,EAAE,UAAU,QAAQ,CAAC,EACnE;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,MAAM,aAAa,gBAAgB,EAAE;YACrC,mBAAmB;YACnB,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;8BAAwG;;;;;;YAI5H,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;8BAAsG;;;;;;YAI1H,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;8BAAkG;;;;;;YAItH,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;8BAAoG;;;;;;YAIxH;gBACE,qBACE,8OAAC;oBAAK,WAAU;8BAAoG;;;;;;QAI1H;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,QAAQ,YAAY,EAAE;QAC3B,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,gBAAgB,OAAO,aAAa,EAAE,KAAK,UAAU;QAE1D,MAAM,cAAc,aAAa,YAAY;QAC7C,IAAI,gBAAgB,aAAa,gBAAgB,MAAM;YACrD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU,YAAY,QAAQ;YAE9C,MAAM,WAAW,MAAM,MACrB,CAAC,6CAA6C,EAAE,aAAa,EAAE,CAAE,KAAK,CAAC,EACvE;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;gBACA,MAAM;YACR;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;YAC9B;QACF,EAAE,OAAM;YACN,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,oBAAoB;YACpB,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oCAAoC;QACpC,aAAa,OAAO,CAClB,uBACA,KAAK,SAAS,CAAC;YACb,cAAc,QAAQ,YAAY;YAClC,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK;YACpB,QAAQ,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAQ,IAAI,KAAK,KAAK,EAAE;QACvD;QAGF,mEAAmE;QACnE,aAAa,OAAO,CAAC,wBAAwB;QAC7C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,cAAc,CAAC,OAA2B,WAAmB,KAAK;QACtE,IAAI,CAAC,OAAO,OAAO;QACnB,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;IAC/B;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,WAAU;YACV,KAAI;;8BAEJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;;;;;;8BAE/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC;4BAEC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,YAAY;;;;;;oDAEtB,eAAe,QAAQ,MAAM;;;;;;;0DAGhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;;oEAAK;oEAAS,QAAQ,QAAQ;;;;;;;;;;;;;kEAEjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;oEAAK;oEACU,WAAW,QAAQ,UAAU;;;;;;;;;;;;;oDAG9C,QAAQ,YAAY,kBACnB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;;gEAA+B;gEAC9B;gEACd,YACC,QAAQ,YAAY,EACpB,QAAQ,aAAa;;;;;;;;;;;;oDAK5B,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;;oEAAM,QAAQ,MAAM,CAAC,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;4CAKlC,QAAQ,KAAK,kBACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAC7B,QAAQ,KAAK;;;;;;;;;;;;4CAKnB,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAyC;;;;;;kEAGtD,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;gEAAiB,WAAU;0EAC1B,cAAA,8OAAC;oEACC,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,KAAK,IAAI,EAAE;oEAClC,QAAO;oEACP,KAAI;oEACJ,WAAU;8EAET,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;;;;;;+DAPpB,KAAK,EAAE;;;;;;;;;;;;;;;;4CAevB,QAAQ,WAAW,kBAClB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,WAAU;sEAAc;;;;;;wDAC7B,QAAQ,WAAW;;;;;;;;;;;;4CAKzB,QAAQ,MAAM,KAAK,YAAY,QAAQ,YAAY,kBAClD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS,IAAM,UAAU;oDACzB,WAAU;;wDACX;wDACK;wDACH,YACC,QAAQ,YAAY,EACpB,QAAQ,aAAa;;;;;;;;;;;;4CAM5B,QAAQ,MAAM,KAAK,wBAClB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;kDAOvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,mBAAmB;oDACnB,iBAAiB;gDACnB;gDACA,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;4CAGhB,QAAQ,MAAM,KAAK,2BAClB,8OAAC;gDACC,SAAS,IAAM,kBAAkB;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BAhIrB,QAAQ,EAAE;;;;;;;;;;;;;;;YA4IxB,iBAAiB,iCAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAiB,gBAAgB,YAAY;;;;;;;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAiB,gBAAgB,QAAQ;;;;;;;;;;;;sDAExD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;gDAChD,eAAe,gBAAgB,MAAM;;;;;;;;;;;;;gCAIzC,gBAAgB,YAAY,kBAC3B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAE,WAAU;sDACV,gBAAgB,YAAY,GACzB,YACE,gBAAgB,YAAY,EAC5B,gBAAgB,aAAa,IAE/B;;;;;;;;;;;;gCAKT,gBAAgB,KAAK,kBACpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAE,WAAU;sDACV,gBAAgB,KAAK;;;;;;;;;;;;gCAK3B,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,CAAC,MAAM,GAAG,mBACvD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAG,WAAU;sDACX,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;oDAAiB,WAAU;8DAC1B,cAAA,8OAAC;wDACC,MAAM,GAAG,6GAAA,CAAA,cAAW,GAAG,KAAK,IAAI,EAAE;wDAClC,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAET,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;;;;;;mDAPpB,KAAK,EAAE;;;;;;;;;;;;;;;;gCAevB,gBAAgB,WAAW,kBAC1B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAE,WAAU;sDACV,gBAAgB,WAAW;;;;;;;;;;;;gCAKjC,gBAAgB,MAAM,CAAC,MAAM,GAAG,mBAC/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,8OAAC;oDAEC,WAAU;oDACV,SAAS;wDACP,iBAAiB,MAAM,KAAK;wDAC5B,kBAAkB;oDACpB;8DAEA,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,GAAG,6GAAA,CAAA,cAAW,GAAG,MAAM,KAAK,EAAE;wDACnC,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;wDACxB,IAAI;wDACJ,WAAU;;;;;;mDAXP;;;;;;;;;;;;;;;;8CAmBf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,mBAAmB,iCAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAqB;wCAE/B,gBAAgB,YAAY;wCAAC;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,mBAAmB;gDACnB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,oBAAoB,8BACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAqB;wCACL;wCAC1B,YACC,aAAa,YAAY,EACzB,aAAa,aAAa;wCACzB;wCAAI;wCAEN,aAAa,YAAY;wCAAC;;;;;;;8CAG7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,oBAAoB;gDACpB,gBAAgB;4CAClB;4CACA,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP,IAAI,gBAAgB,OAAO,aAAa,EAAE,KAAK,UAAU;oDACvD;gDACF;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kBAAkB,+BACjB,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,kBAAkB;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK,GAAG,6GAAA,CAAA,cAAW,GAAG,eAAe;4BACrC,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC;uCAEe", "debugId": null}}, {"offset": {"line": 6592, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/BalanceRequestsManagement.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  CreditCard,\n  Eye,\n  Calendar,\n  CheckCircle,\n  Clock,\n  XCircle,\n  DollarSign,\n} from \"lucide-react\";\nimport { BalanceRequest } from \"@/types/account\";\nimport { formatCurrency } from \"@/utils/currencyUtils\";\n\ninterface ExtendedBalanceRequest extends BalanceRequest {\n  user_currency: string;\n  user_name?: string;\n  user_email?: string;\n  product_name?: string;\n  quantity?: number;\n  quoted_price?: string | number;\n  processed_by_email?: string;\n  images?: { id?: number; image: string }[];\n  files?: { id?: number; file: string }[];\n  transaction_file?: string | null;\n}\n\nconst BalanceRequestsManagement: React.FC = () => {\n  const [balanceRequests, setBalanceRequests] = useState<\n    ExtendedBalanceRequest[]\n  >([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedRequest, setSelectedRequest] =\n    useState<ExtendedBalanceRequest | null>(null);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showImageModal, setShowImageModal] = useState(false);\n\n  useEffect(() => {\n    fetchBalanceRequests();\n  }, []);\n\n  const fetchBalanceRequests = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        \"https://api.imdadport.com/api/balance/my-requests/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        setBalanceRequests(data);\n      } else {\n        toast.error(\"فشل في تحميل طلبات الرصيد\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching balance requests:\", error);\n      toast.error(\"فشل في تحميل طلبات الرصيد\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"pending\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-3 h-3 ml-1\" />\n            قيد المراجعة\n          </span>\n        );\n      case \"approved\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n            <CheckCircle className=\"w-3 h-3 ml-1\" />\n            مقبول\n          </span>\n        );\n      case \"rejected\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-3 h-3 ml-1\" />\n            مرفوض\n          </span>\n        );\n      default:\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            غير محدد\n          </span>\n        );\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  if (loading) {\n    return (\n      <div\n        className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n        dir=\"rtl\"\n      >\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <h3 className=\"text-xl font-bold text-white\">إدارة الرصيد</h3>\n        </div>\n        <div className=\"p-8 flex justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\" dir=\"rtl\">\n      {/* Balance Requests */}\n      <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <h3 className=\"text-xl font-bold text-white flex items-center\">\n            <DollarSign className=\"w-5 h-5 ml-2 text-green-400\" />\n            طلبات الرصيد\n          </h3>\n          <p className=\"text-sm text-gray-300 mt-1\">\n            تتبع وإدارة طلبات إضافة الرصيد الخاصة بك\n          </p>\n        </div>\n\n        <div className=\"p-6\">\n          {balanceRequests.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <CreditCard className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">\n                لا توجد طلبات رصيد\n              </h3>\n              <p className=\"text-gray-500\">لم تقم بإرسال أي طلبات رصيد بعد</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {balanceRequests.map((request) => (\n                <div\n                  key={request.id}\n                  className=\"border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-3 mb-2\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">\n                          {formatCurrency(\n                            request.amount,\n                            request.user_currency\n                          )}\n                        </h4>\n                        {request.approved_amount && (\n                          <span className=\"text-sm text-green-600\">\n                            المبلغ المعتمد:{\" \"}\n                            {formatCurrency(\n                              request.approved_amount,\n                              request.user_currency\n                            )}\n                          </span>\n                        )}\n                        {getStatusBadge(request.status)}\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3\">\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"w-4 h-4 ml-2\" />\n                          <span>\n                            تاريخ الطلب: {formatDate(request.created_at)}\n                          </span>\n                        </div>\n                      </div>\n\n                      {request.notes && (\n                        <div className=\"mb-3\">\n                          <p className=\"text-sm text-gray-600 bg-gray-50 p-2 rounded\">\n                            <span className=\"font-medium\">الملاحظات: </span>\n                            {request.notes}\n                          </p>\n                        </div>\n                      )}\n\n                      {request.admin_notes && (\n                        <div className=\"mb-3\">\n                          <p className=\"text-sm text-blue-600 bg-blue-50 p-2 rounded\">\n                            <span className=\"font-medium\">\n                              ملاحظات الإدارة:{\" \"}\n                            </span>\n                            {request.admin_notes}\n                          </p>\n                        </div>\n                      )}\n\n                      {request.status === \"approved\" &&\n                        request.processed_at && (\n                          <div className=\"text-sm text-green-600\">\n                            <span className=\"font-medium\">\n                              تمت الموافقة في:{\" \"}\n                            </span>\n                            {formatDate(request.processed_at)}\n                          </div>\n                        )}\n\n                      {request.status === \"rejected\" &&\n                        request.processed_at && (\n                          <div className=\"text-sm text-red-600\">\n                            <span className=\"font-medium\">تم الرفض في: </span>\n                            {formatDate(request.processed_at)}\n                          </div>\n                        )}\n                    </div>\n\n                    <div className=\"flex items-center gap-2 mr-4\">\n                      <button\n                        onClick={() => {\n                          setSelectedRequest(request);\n                          setShowViewModal(true);\n                        }}\n                        className=\"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors cursor-pointer\"\n                        title=\"عرض التفاصيل\"\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* View Modal */}\n      {showViewModal && selectedRequest && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div\n            className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\"\n            dir=\"rtl\"\n          >\n            <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">\n                تفاصيل طلب الرصيد\n              </h3>\n            </div>\n\n            <div className=\"p-6 space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    رقم الطلب\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {selectedRequest.id ?? \"غير متوفر\"}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    اسم المستخدم\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {selectedRequest.user_name ?? \"غير متوفر\"}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    البريد الإلكتروني\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {selectedRequest.user_email ?? \"غير متوفر\"}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">العملة</h4>\n                  <p className=\"text-gray-600\">\n                    {selectedRequest.user_currency ?? \"غير متوفر\"}\n                  </p>\n                </div>\n                {selectedRequest.product_name && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      اسم المنتج\n                    </h4>\n                    <p className=\"text-gray-600\">\n                      {selectedRequest.product_name}\n                    </p>\n                  </div>\n                )}\n                {selectedRequest.quantity && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">الكمية</h4>\n                    <p className=\"text-gray-600\">{selectedRequest.quantity}</p>\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    المبلغ المطلوب\n                  </h4>\n                  <p className=\"text-2xl font-bold text-green-600\">\n                    {formatCurrency(\n                      selectedRequest.amount,\n                      selectedRequest.user_currency\n                    )}\n                  </p>\n                </div>\n                {selectedRequest.quoted_price && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      المبلغ المقترح\n                    </h4>\n                    <p className=\"text-gray-600\">\n                      {selectedRequest.quoted_price}\n                    </p>\n                  </div>\n                )}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">الحالة</h4>\n                  {getStatusBadge(selectedRequest.status)}\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    تاريخ الطلب\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {formatDate(selectedRequest.created_at)}\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">\n                    آخر تحديث\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {formatDate(selectedRequest.updated_at)}\n                  </p>\n                </div>\n                {selectedRequest.processed_by_email && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      تمت المعالجة بواسطة\n                    </h4>\n                    <p className=\"text-gray-600\">\n                      {selectedRequest.processed_by_email}\n                    </p>\n                  </div>\n                )}\n                {selectedRequest.processed_at && (\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      تاريخ المعالجة\n                    </h4>\n                    <p className=\"text-gray-600\">\n                      {formatDate(selectedRequest.processed_at)}\n                    </p>\n                  </div>\n                )}\n                {selectedRequest.admin_notes && (\n                  <div className=\"col-span-2\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      ملاحظات الإدارة\n                    </h4>\n                    <p className=\"text-blue-600 bg-blue-50 p-3 rounded\">\n                      {selectedRequest.admin_notes}\n                    </p>\n                  </div>\n                )}\n                {selectedRequest.notes && (\n                  <div className=\"col-span-2\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      ملاحظات المستخدم\n                    </h4>\n                    <p className=\"text-gray-600 bg-gray-50 p-3 rounded\">\n                      {selectedRequest.notes}\n                    </p>\n                  </div>\n                )}\n                {/* Approved Amount */}\n                {selectedRequest.approved_amount !== undefined &&\n                  selectedRequest.approved_amount !== null && (\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">\n                        المبلغ المعتمد\n                      </h4>\n                      <p className=\"text-green-700 font-bold\">\n                        {formatCurrency(\n                          selectedRequest.approved_amount,\n                          selectedRequest.user_currency\n                        )}\n                      </p>\n                    </div>\n                  )}\n                {/* Images */}\n                {selectedRequest.images &&\n                  selectedRequest.images.length > 0 && (\n                    <div className=\"col-span-2\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">\n                        الصور\n                      </h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {selectedRequest.images.map((img, idx) => (\n                          <img\n                            key={img.id || idx}\n                            src={\n                              img.image.startsWith(\"http\")\n                                ? img.image\n                                : `https://api.imdadport.com${img.image}`\n                            }\n                            alt={`صورة ${idx + 1}`}\n                            className=\"w-24 h-24 object-cover rounded border border-gray-200\"\n                          />\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                {/* Files */}\n                {selectedRequest.files && selectedRequest.files.length > 0 && (\n                  <div className=\"col-span-2\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      الملفات\n                    </h4>\n                    <ul className=\"list-disc pr-6\">\n                      {selectedRequest.files.map((file, idx) => (\n                        <li key={file.id || idx} className=\"mb-1\">\n                          <a\n                            href={\n                              file.file.startsWith(\"http\")\n                                ? file.file\n                                : `https://api.imdadport.com${file.file}`\n                            }\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-blue-600 hover:underline\"\n                          >\n                            ملف {idx + 1}\n                          </a>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                {selectedRequest.transaction_file && (\n                  <div className=\"col-span-2\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">\n                      ملف المعاملة\n                    </h4>\n                    {selectedRequest.transaction_file.match(\n                      /\\.(jpg|jpeg|png|gif|bmp|webp)$/i\n                    ) ? (\n                      <img\n                        src={`https://api.imdadport.com${selectedRequest.transaction_file}`}\n                        alt=\"ملف المعاملة\"\n                        className=\"w-24 h-24 object-cover rounded border border-gray-200 mb-2\"\n                      />\n                    ) : null}\n                    <a\n                      href={`https://api.imdadport.com${selectedRequest.transaction_file}`}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-blue-600 hover:underline\"\n                    >\n                      عرض / تحميل الملف\n                    </a>\n                  </div>\n                )}\n              </div>\n              <div className=\"flex justify-end pt-4\">\n                <button\n                  onClick={() => setShowViewModal(false)}\n                  className=\"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors cursor-pointer\"\n                >\n                  إغلاق\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Modal */}\n      {showImageModal &&\n        selectedRequest &&\n        selectedRequest.images &&\n        selectedRequest.images.length > 0 && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[60] p-4\"\n            onClick={() => setShowImageModal(false)}\n          >\n            <div className=\"relative max-w-[90vw] max-h-[90vh]\">\n              <img\n                src={`https://api.imdadport.com/${selectedRequest.images[0].image}`}\n                alt=\"صورة إيصال التحويل\"\n                className=\"max-w-full max-h-[90vh] object-contain rounded-lg\"\n              />\n              <button\n                onClick={() => setShowImageModal(false)}\n                className=\"absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 transition-colors\"\n              >\n                <XCircle className=\"w-6 h-6 text-gray-600\" />\n              </button>\n            </div>\n          </div>\n        )}\n    </div>\n  );\n};\n\nexport default BalanceRequestsManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AA6BA,MAAM,4BAAsC;IAC1C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEnD,EAAE;IACJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GACzC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,sDACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB;YACrB,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAIxC,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI9C,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI1C;gBACE,qBACE,8OAAC;oBAAK,WAAU;8BAAoG;;;;;;QAI1H;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,WAAU;YACV,KAAI;;8BAEJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;;;;;;8BAE/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAY,KAAI;;0BAE7B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAgC;;;;;;;0CAGxD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;iDAG/B,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EACZ,QAAQ,MAAM,EACd,QAAQ,aAAa;;;;;;4DAGxB,QAAQ,eAAe,kBACtB,8OAAC;gEAAK,WAAU;;oEAAyB;oEACvB;oEACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EACZ,QAAQ,eAAe,EACvB,QAAQ,aAAa;;;;;;;4DAI1B,eAAe,QAAQ,MAAM;;;;;;;kEAGhC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;;wEAAK;wEACU,WAAW,QAAQ,UAAU;;;;;;;;;;;;;;;;;;oDAKhD,QAAQ,KAAK,kBACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;oEAAK,WAAU;8EAAc;;;;;;gEAC7B,QAAQ,KAAK;;;;;;;;;;;;oDAKnB,QAAQ,WAAW,kBAClB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;oEAAK,WAAU;;wEAAc;wEACX;;;;;;;gEAElB,QAAQ,WAAW;;;;;;;;;;;;oDAKzB,QAAQ,MAAM,KAAK,cAClB,QAAQ,YAAY,kBAClB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAc;oEACX;;;;;;;4DAElB,WAAW,QAAQ,YAAY;;;;;;;oDAIrC,QAAQ,MAAM,KAAK,cAClB,QAAQ,YAAY,kBAClB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAC7B,WAAW,QAAQ,YAAY;;;;;;;;;;;;;0DAKxC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,SAAS;wDACP,mBAAmB;wDACnB,iBAAiB;oDACnB;oDACA,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;mCAjFhB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YA6F1B,iBAAiB,iCAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,KAAI;;sCAEJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,EAAE,IAAI;;;;;;;;;;;;sDAG3B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,SAAS,IAAI;;;;;;;;;;;;sDAGlC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,UAAU,IAAI;;;;;;;;;;;;sDAGnC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,aAAa,IAAI;;;;;;;;;;;;wCAGrC,gBAAgB,YAAY,kBAC3B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,YAAY;;;;;;;;;;;;wCAIlC,gBAAgB,QAAQ,kBACvB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAiB,gBAAgB,QAAQ;;;;;;;;;;;;sDAG1D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EACZ,gBAAgB,MAAM,EACtB,gBAAgB,aAAa;;;;;;;;;;;;wCAIlC,gBAAgB,YAAY,kBAC3B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,YAAY;;;;;;;;;;;;sDAInC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;gDAChD,eAAe,gBAAgB,MAAM;;;;;;;sDAExC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,WAAW,gBAAgB,UAAU;;;;;;;;;;;;sDAG1C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,WAAW,gBAAgB,UAAU;;;;;;;;;;;;wCAGzC,gBAAgB,kBAAkB,kBACjC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,kBAAkB;;;;;;;;;;;;wCAIxC,gBAAgB,YAAY,kBAC3B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,WAAW,gBAAgB,YAAY;;;;;;;;;;;;wCAI7C,gBAAgB,WAAW,kBAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,WAAW;;;;;;;;;;;;wCAIjC,gBAAgB,KAAK,kBACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,gBAAgB,KAAK;;;;;;;;;;;;wCAK3B,gBAAgB,eAAe,KAAK,aACnC,gBAAgB,eAAe,KAAK,sBAClC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EACZ,gBAAgB,eAAe,EAC/B,gBAAgB,aAAa;;;;;;;;;;;;wCAMtC,gBAAgB,MAAM,IACrB,gBAAgB,MAAM,CAAC,MAAM,GAAG,mBAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,oBAChC,8OAAC;4DAEC,KACE,IAAI,KAAK,CAAC,UAAU,CAAC,UACjB,IAAI,KAAK,GACT,CAAC,yBAAyB,EAAE,IAAI,KAAK,EAAE;4DAE7C,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG;4DACtB,WAAU;2DAPL,IAAI,EAAE,IAAI;;;;;;;;;;;;;;;;wCAc1B,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,CAAC,MAAM,GAAG,mBACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DAGjD,8OAAC;oDAAG,WAAU;8DACX,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBAChC,8OAAC;4DAAwB,WAAU;sEACjC,cAAA,8OAAC;gEACC,MACE,KAAK,IAAI,CAAC,UAAU,CAAC,UACjB,KAAK,IAAI,GACT,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;gEAE7C,QAAO;gEACP,KAAI;gEACJ,WAAU;;oEACX;oEACM,MAAM;;;;;;;2DAXN,KAAK,EAAE,IAAI;;;;;;;;;;;;;;;;wCAkB3B,gBAAgB,gBAAgB,kBAC/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;gDAGhD,gBAAgB,gBAAgB,CAAC,KAAK,CACrC,mDAEA,8OAAC;oDACC,KAAK,CAAC,yBAAyB,EAAE,gBAAgB,gBAAgB,EAAE;oDACnE,KAAI;oDACJ,WAAU;;;;;2DAEV;8DACJ,8OAAC;oDACC,MAAM,CAAC,yBAAyB,EAAE,gBAAgB,gBAAgB,EAAE;oDACpE,QAAO;oDACP,KAAI;oDACJ,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kBACC,mBACA,gBAAgB,MAAM,IACtB,gBAAgB,MAAM,CAAC,MAAM,GAAG,mBAC9B,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,kBAAkB;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK,CAAC,0BAA0B,EAAE,gBAAgB,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE;4BACnE,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;uCAEe", "debugId": null}}, {"offset": {"line": 7672, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/SummaryCards.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { DollarSign, TrendingUp, TrendingDown } from \"lucide-react\";\nimport { Transaction } from \"@/types/account\";\n\ninterface SummaryCardsProps {\n  className?: string;\n  userCurrency?: string;\n}\n\nconst SummaryCards: React.FC<SummaryCardsProps> = ({\n  className = \"\",\n  userCurrency,\n}) => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [currentBalance, setCurrentBalance] = useState<number>(0);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      // Fetch user balance\n      const balanceResponse = await fetch(\n        \"https://api.imdadport.com/api/auth/user-data\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      // Fetch transactions\n      const transactionsResponse = await fetch(\n        \"https://api.imdadport.com/api/transactions/\",\n        {\n          headers: {\n            Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n          },\n        }\n      );\n\n      if (balanceResponse.ok) {\n        const userData = await balanceResponse.json();\n        setCurrentBalance(userData.balance || 0);\n      }\n\n      if (transactionsResponse.ok) {\n        const transactionsData = await transactionsResponse.json();\n        setTransactions(transactionsData);\n      }\n    } catch (error) {\n      console.error(\"Error fetching summary data:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat(\"ar-EG\", {\n      style: \"currency\",\n      currency: typeof userCurrency === \"string\" ? userCurrency : \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0,\n    }).format(amount);\n  };\n\n  const getTotalCredits = () => {\n    return transactions\n      .filter((t) => t.transaction_type === \"balance_add\")\n      .reduce((sum, t) => sum + Number(t.amount), 0);\n  };\n\n  const getTotalDebits = () => {\n    return (\n      transactions\n        // Accept both 'price_request' and 'price_request_payment' as debit types\n        .filter((t) => {\n          const type = t.transaction_type as string;\n          return type === \"price_request\" || type === \"price_request_payment\";\n        })\n        .reduce((sum, t) => sum + Number(t.amount), 0)\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n        {[1, 2, 3].map((i) => (\n          <div\n            key={i}\n            className=\"bg-white rounded-xl shadow-lg border border-gray-100 p-6\"\n          >\n            <div className=\"animate-pulse\">\n              <div className=\"bg-gray-200 h-4 w-24 rounded mb-2\"></div>\n              <div className=\"bg-gray-200 h-8 w-32 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      {/* Current Balance */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg text-white p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-sm font-semibold mb-1\">الرصيد المتاح</h3>\n            <p className=\"text-2xl font-bold\">\n              {formatCurrency(currentBalance)}\n            </p>\n          </div>\n          <div className=\"bg-white bg-opacity-20 rounded-full p-3 text-blue-700\">\n            <DollarSign className=\"w-6 h-6\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Total Credits */}\n      <div className=\"bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg text-white p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-sm font-semibold mb-1\">إجمالي الإيداعات</h3>\n            <p className=\"text-2xl font-bold\">\n              {formatCurrency(getTotalCredits())}\n            </p>\n          </div>\n          <div className=\"bg-white bg-opacity-20 rounded-full p-3 text-green-700\">\n            <TrendingUp className=\"w-6 h-6\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Total Debits */}\n      <div className=\"bg-gradient-to-r from-red-600 to-red-700 rounded-xl shadow-lg text-white p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-sm font-semibold mb-1\">إجمالي المدفوعات</h3>\n            <p className=\"text-2xl font-bold\">\n              {formatCurrency(getTotalDebits())}\n            </p>\n          </div>\n          <div className=\"bg-white bg-opacity-20 rounded-full p-3 text-red-700  \">\n            <TrendingDown className=\"w-6 h-6\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SummaryCards;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAWA,MAAM,eAA4C,CAAC,EACjD,YAAY,EAAE,EACd,YAAY,EACb;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,qBAAqB;YACrB,MAAM,kBAAkB,MAAM,MAC5B,gDACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,qBAAqB;YACrB,MAAM,uBAAuB,MAAM,MACjC,+CACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAGF,IAAI,gBAAgB,EAAE,EAAE;gBACtB,MAAM,WAAW,MAAM,gBAAgB,IAAI;gBAC3C,kBAAkB,SAAS,OAAO,IAAI;YACxC;YAEA,IAAI,qBAAqB,EAAE,EAAE;gBAC3B,MAAM,mBAAmB,MAAM,qBAAqB,IAAI;gBACxD,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU,OAAO,iBAAiB,WAAW,eAAe;YAC5D,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB;QACtB,OAAO,aACJ,MAAM,CAAC,CAAC,IAAM,EAAE,gBAAgB,KAAK,eACrC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,OAAO,EAAE,MAAM,GAAG;IAChD;IAEA,MAAM,iBAAiB;QACrB,OACE,YACE,yEAAyE;SACxE,MAAM,CAAC,CAAC;YACP,MAAM,OAAO,EAAE,gBAAgB;YAC/B,OAAO,SAAS,mBAAmB,SAAS;QAC9C,GACC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,OAAO,EAAE,MAAM,GAAG;IAElD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBACjE;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oBAEC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;mBALZ;;;;;;;;;;IAWf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,sCAAsC,EAAE,WAAW;;0BAElE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CACV,eAAe;;;;;;;;;;;;sCAGpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CACV,eAAe;;;;;;;;;;;;sCAGpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CACV,eAAe;;;;;;;;;;;;sCAGpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;uCAEe", "debugId": null}}, {"offset": {"line": 7959, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/utils/addressUtils.ts"], "sourcesContent": ["import { AddressFormData, FormErrors } from \"@/types/account\";\n\nexport const validateAddressForm = (formData: AddressFormData): FormErrors => {\n  const errors: FormErrors = {};\n\n  // Country validation\n  if (!formData.country || formData.country.trim() === \"\") {\n    errors.country = \"الدولة مطلوبة\";\n  } else if (formData.country.trim().length < 2) {\n    errors.country = \"اسم الدولة قصير جداً\";\n  } else if (formData.country.trim().length > 50) {\n    errors.country = \"اسم الدولة طويل جداً\";\n  }\n\n  // Governorate validation\n  if (!formData.governorate || formData.governorate.trim() === \"\") {\n    errors.governorate = \"المحافظة مطلوبة\";\n  } else if (formData.governorate.trim().length < 2) {\n    errors.governorate = \"اسم المحافظة قصير جداً\";\n  } else if (formData.governorate.trim().length > 50) {\n    errors.governorate = \"اسم المحافظة طويل جداً\";\n  }\n\n  // City validation\n  if (!formData.city || formData.city.trim() === \"\") {\n    errors.city = \"المدينة مطلوبة\";\n  } else if (formData.city.trim().length < 2) {\n    errors.city = \"اسم المدينة قصير جداً\";\n  } else if (formData.city.trim().length > 50) {\n    errors.city = \"اسم المدينة طويل جداً\";\n  }\n\n  // Zipcode validation\n  if (!formData.zipcode || formData.zipcode.trim() === \"\") {\n    errors.zipcode = \"الرمز البريدي مطلوب\";\n  } else if (!/^\\d{4,10}$/.test(formData.zipcode.trim())) {\n    errors.zipcode = \"الرمز البريدي يجب أن يكون من 4 إلى 10 أرقام\";\n  }\n\n  // Address validation\n  if (!formData.address || formData.address.trim() === \"\") {\n    errors.address = \"العنوان التفصيلي مطلوب\";\n  } else if (formData.address.trim().length < 10) {\n    errors.address = \"العنوان التفصيلي قصير جداً (على الأقل 10 أحرف)\";\n  } else if (formData.address.trim().length > 200) {\n    errors.address = \"العنوان التفصيلي طويل جداً (حد أقصى 200 حرف)\";\n  }\n\n  // Nearest port validation\n  if (!formData.nearest_port || formData.nearest_port.trim() === \"\") {\n    errors.nearest_port = \"أقرب ميناء مطلوب\";\n  } else if (formData.nearest_port.trim().length < 2) {\n    errors.nearest_port = \"اسم الميناء قصير جداً\";\n  } else if (formData.nearest_port.trim().length > 100) {\n    errors.nearest_port = \"اسم الميناء طويل جداً\";\n  }\n\n  // Nearest airport validation\n  if (!formData.nearest_airport || formData.nearest_airport.trim() === \"\") {\n    errors.nearest_airport = \"أقرب مطار دولي مطلوب\";\n  } else if (formData.nearest_airport.trim().length < 2) {\n    errors.nearest_airport = \"اسم المطار قصير جداً\";\n  } else if (formData.nearest_airport.trim().length > 100) {\n    errors.nearest_airport = \"اسم المطار طويل جداً\";\n  }\n\n  return errors;\n};\n\nexport const isAddressComplete = (user: any): boolean => {\n  console.log(\"isAddressComplete\", user);\n  return !!(\n    user?.country &&\n    user?.city &&\n    user?.governorate &&\n    user?.zipcode &&\n    user?.address &&\n    user?.nearest_port &&\n    user?.nearest_airport\n  );\n};\n\nexport const isProfileComplete = (user: any): boolean => {\n  return !!(\n    user?.first_name &&\n    user?.last_name &&\n    user?.email &&\n    user?.phone_number &&\n    isAddressComplete(user)\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAAqB,CAAC;IAE5B,qBAAqB;IACrB,IAAI,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,OAAO,IAAI;QACvD,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QAC7C,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;QAC9C,OAAO,OAAO,GAAG;IACnB;IAEA,yBAAyB;IACzB,IAAI,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,IAAI,OAAO,IAAI;QAC/D,OAAO,WAAW,GAAG;IACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACjD,OAAO,WAAW,GAAG;IACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;QAClD,OAAO,WAAW,GAAG;IACvB;IAEA,kBAAkB;IAClB,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,OAAO,IAAI;QACjD,OAAO,IAAI,GAAG;IAChB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QAC1C,OAAO,IAAI,GAAG;IAChB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;QAC3C,OAAO,IAAI,GAAG;IAChB;IAEA,qBAAqB;IACrB,IAAI,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,OAAO,IAAI;QACvD,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,KAAK;QACtD,OAAO,OAAO,GAAG;IACnB;IAEA,qBAAqB;IACrB,IAAI,CAAC,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,OAAO,IAAI;QACvD,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;QAC9C,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;QAC/C,OAAO,OAAO,GAAG;IACnB;IAEA,0BAA0B;IAC1B,IAAI,CAAC,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,IAAI,OAAO,IAAI;QACjE,OAAO,YAAY,GAAG;IACxB,OAAO,IAAI,SAAS,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QAClD,OAAO,YAAY,GAAG;IACxB,OAAO,IAAI,SAAS,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;QACpD,OAAO,YAAY,GAAG;IACxB;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,IAAI,OAAO,IAAI;QACvE,OAAO,eAAe,GAAG;IAC3B,OAAO,IAAI,SAAS,eAAe,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;QACrD,OAAO,eAAe,GAAG;IAC3B,OAAO,IAAI,SAAS,eAAe,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK;QACvD,OAAO,eAAe,GAAG;IAC3B;IAEA,OAAO;AACT;AAEO,MAAM,oBAAoB,CAAC;IAChC,QAAQ,GAAG,CAAC,qBAAqB;IACjC,OAAO,CAAC,CAAC,CACP,MAAM,WACN,MAAM,QACN,MAAM,eACN,MAAM,WACN,MAAM,WACN,MAAM,gBACN,MAAM,eACR;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAC,CAAC,CACP,MAAM,cACN,MAAM,aACN,MAAM,SACN,MAAM,gBACN,kBAAkB,KACpB;AACF", "debugId": null}}, {"offset": {"line": 8035, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/Address.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { toast } from \"react-toastify\";\nimport { MapPin, Loader2, ArrowRight } from \"lucide-react\";\nimport Link from \"next/link\";\nimport Input from \"@/components/common/Input\";\nimport Layout from \"@/components/layout/Layout\";\nimport { AddressFormData, FormErrors } from \"@/types/account\";\nimport { getUserProfile, updateUserProfile } from \"@/services/authService\";\nimport { validateAddressForm } from \"@/utils/addressUtils\";\n\nconst AddressPage = () => {\n  const [loading, setLoading] = useState(true);\n  const [updateLoading, setUpdateLoading] = useState(false);\n  const [hasExistingAddress, setHasExistingAddress] = useState(false);\n  const [errors, setErrors] = useState<FormErrors>({});\n\n  const [formData, setFormData] = useState<AddressFormData>({\n    country: \"\",\n    city: \"\",\n    governorate: \"\",\n    zipcode: \"\",\n    address: \"\",\n    nearest_port: \"\",\n    nearest_airport: \"\",\n  });\n\n  // Fetch user profile to check if address exists\n  useEffect(() => {\n    fetchUserProfile();\n  }, []);\n\n  const fetchUserProfile = async () => {\n    setLoading(true);\n    try {\n      const data = await getUserProfile();\n\n      if (data.address || data.city || data.governorate || data.country) {\n        setHasExistingAddress(true);\n        setFormData({\n          country: data.country || \"\",\n          city: data.city || \"\",\n          governorate: data.governorate || \"\",\n          zipcode: data.zipcode || \"\",\n          address: data.address || \"\",\n          nearest_port: data.nearest_port || \"\",\n          nearest_airport: data.nearest_airport || \"\",\n        });\n      }\n\n      setLoading(false);\n    } catch (error) {\n      console.error(\"Error fetching user profile:\", error);\n      toast.error(\"فشل في تحميل بيانات العنوان\");\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate form\n    const validationErrors = validateAddressForm(formData);\n    setErrors(validationErrors);\n\n    if (Object.keys(validationErrors).length > 0) {\n      toast.error(\"يرجى تصحيح الأخطاء في النموذج\");\n      return;\n    }\n\n    setUpdateLoading(true);\n\n    try {\n      // Create FormData object\n      const submitData = new FormData();\n      submitData.append(\"country\", formData.country);\n      submitData.append(\"city\", formData.city);\n      submitData.append(\"governorate\", formData.governorate);\n      submitData.append(\"zipcode\", formData.zipcode);\n      submitData.append(\"address\", formData.address);\n      submitData.append(\"nearest_port\", formData.nearest_port);\n      submitData.append(\"nearest_airport\", formData.nearest_airport);\n\n      await updateUserProfile(submitData);\n\n      toast.success(\n        hasExistingAddress\n          ? \"تم تحديث العنوان بنجاح\"\n          : \"تم إضافة العنوان بنجاح\",\n        {\n          autoClose: 1500,\n          hideProgressBar: true,\n          closeOnClick: true,\n          pauseOnHover: false,\n          draggable: true,\n        }\n      );\n\n      // Redirect back to account page after success\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error) {\n      console.error(\"Error updating address:\", error);\n      toast.error(\"فشل في حفظ العنوان\");\n    } finally {\n      setUpdateLoading(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof AddressFormData, value: string) => {\n    setFormData((prev) => ({ ...prev, [field]: value }));\n    // Clear error for this field\n    if (errors[field]) {\n      setErrors((prev) => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-gray-900 to-black px-8 py-6\">\n        <h3 className=\"text-2xl font-bold text-white flex items-center\">\n          <MapPin className=\"w-6 h-6 ml-3 text-blue-400\" />\n          معلومات العنوان\n        </h3>\n        <p className=\"text-sm text-gray-300 mt-2\">\n          {hasExistingAddress ? \"تحديث بيانات عنوانك\" : \"إضافة بيانات عنوانك\"}\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"p-8 space-y-8\" noValidate>\n        <div className=\"bg-gray-50 rounded-xl p-8 border border-gray-200\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-8 flex items-center\">\n            <MapPin className=\"w-5 h-5 ml-2 text-gray-600\" />\n            بيانات العنوان\n          </h4>\n          <div className=\"space-y-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <Input\n                id=\"country\"\n                label=\"الدولة\"\n                type=\"text\"\n                placeholder=\"مثال: مصر\"\n                value={formData.country}\n                onChange={(e) => handleInputChange(\"country\", e.target.value)}\n                error={!!errors.country}\n                errorMessage={errors.country}\n                required\n              />\n\n              <Input\n                id=\"governorate\"\n                label=\"المحافظة\"\n                type=\"text\"\n                placeholder=\"مثال: القاهرة\"\n                value={formData.governorate}\n                onChange={(e) =>\n                  handleInputChange(\"governorate\", e.target.value)\n                }\n                error={!!errors.governorate}\n                errorMessage={errors.governorate}\n                required\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <Input\n                id=\"city\"\n                label=\"المدينة\"\n                type=\"text\"\n                placeholder=\"مثال: مدينة نصر\"\n                value={formData.city}\n                onChange={(e) => handleInputChange(\"city\", e.target.value)}\n                error={!!errors.city}\n                errorMessage={errors.city}\n                required\n              />\n\n              <Input\n                id=\"zipcode\"\n                label=\"الرمز البريدي\"\n                type=\"text\"\n                placeholder=\"مثال: 12345\"\n                value={formData.zipcode}\n                onChange={(e) => handleInputChange(\"zipcode\", e.target.value)}\n                error={!!errors.zipcode}\n                errorMessage={errors.zipcode}\n                required\n              />\n            </div>\n\n            <div>\n              <Input\n                id=\"address\"\n                label=\"العنوان التفصيلي\"\n                type=\"text\"\n                placeholder=\"مثال: شارع التحرير، مبنى رقم 15، الدور الثالث\"\n                value={formData.address}\n                onChange={(e) => handleInputChange(\"address\", e.target.value)}\n                error={!!errors.address}\n                errorMessage={errors.address}\n                required\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <Input\n                id=\"nearest_port\"\n                label=\"أقرب ميناء\"\n                type=\"text\"\n                placeholder=\"مثال: ميناء الإسكندرية\"\n                value={formData.nearest_port}\n                onChange={(e) =>\n                  handleInputChange(\"nearest_port\", e.target.value)\n                }\n                error={!!errors.nearest_port}\n                errorMessage={errors.nearest_port}\n                required\n              />\n\n              <Input\n                id=\"nearest_airport\"\n                label=\"أقرب مطار دولي\"\n                type=\"text\"\n                placeholder=\"مثال: مطار القاهرة الدولي\"\n                value={formData.nearest_airport}\n                onChange={(e) =>\n                  handleInputChange(\"nearest_airport\", e.target.value)\n                }\n                error={!!errors.nearest_airport}\n                errorMessage={errors.nearest_airport}\n                required\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"pt-6\">\n          <button\n            type=\"submit\"\n            disabled={updateLoading}\n            className=\"w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200\"\n          >\n            {updateLoading ? (\n              <>\n                <Loader2 className=\"w-5 h-5 animate-spin ml-2\" />\n                {hasExistingAddress ? \"جاري التحديث...\" : \"جاري الحفظ...\"}\n              </>\n            ) : hasExistingAddress ? (\n              \"تحديث العنوان\"\n            ) : (\n              \"حفظ العنوان\"\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AddressPage;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAEA;AAGA;AACA;AAXA;;;;;;;;AAaA,MAAM,cAAc;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAElD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,SAAS;QACT,MAAM;QACN,aAAa;QACb,SAAS;QACT,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO,EAAE;gBACjE,sBAAsB;gBACtB,YAAY;oBACV,SAAS,KAAK,OAAO,IAAI;oBACzB,MAAM,KAAK,IAAI,IAAI;oBACnB,aAAa,KAAK,WAAW,IAAI;oBACjC,SAAS,KAAK,OAAO,IAAI;oBACzB,SAAS,KAAK,OAAO,IAAI;oBACzB,cAAc,KAAK,YAAY,IAAI;oBACnC,iBAAiB,KAAK,eAAe,IAAI;gBAC3C;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,MAAM,mBAAmB,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;QAC7C,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;YAC5C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iBAAiB;QAEjB,IAAI;YACF,yBAAyB;YACzB,MAAM,aAAa,IAAI;YACvB,WAAW,MAAM,CAAC,WAAW,SAAS,OAAO;YAC7C,WAAW,MAAM,CAAC,QAAQ,SAAS,IAAI;YACvC,WAAW,MAAM,CAAC,eAAe,SAAS,WAAW;YACrD,WAAW,MAAM,CAAC,WAAW,SAAS,OAAO;YAC7C,WAAW,MAAM,CAAC,WAAW,SAAS,OAAO;YAC7C,WAAW,MAAM,CAAC,gBAAgB,SAAS,YAAY;YACvD,WAAW,MAAM,CAAC,mBAAmB,SAAS,eAAe;YAE7D,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;YAExB,mJAAA,CAAA,QAAK,CAAC,OAAO,CACX,qBACI,2BACA,0BACJ;gBACE,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAGF,8CAA8C;YAC9C,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAClD,6BAA6B;QAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACtD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAA+B;;;;;;;kCAGnD,8OAAC;wBAAE,WAAU;kCACV,qBAAqB,wBAAwB;;;;;;;;;;;;0BAIlD,8OAAC;gBAAK,UAAU;gBAAc,WAAU;gBAAgB,UAAU;;kCAChE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC5D,OAAO,CAAC,CAAC,OAAO,OAAO;gDACvB,cAAc,OAAO,OAAO;gDAC5B,QAAQ;;;;;;0DAGV,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gDAEjD,OAAO,CAAC,CAAC,OAAO,WAAW;gDAC3B,cAAc,OAAO,WAAW;gDAChC,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,OAAO,CAAC,CAAC,OAAO,IAAI;gDACpB,cAAc,OAAO,IAAI;gDACzB,QAAQ;;;;;;0DAGV,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC5D,OAAO,CAAC,CAAC,OAAO,OAAO;gDACvB,cAAc,OAAO,OAAO;gDAC5B,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;kDACC,cAAA,8OAAC,qIAAA,CAAA,UAAK;4CACJ,IAAG;4CACH,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC5D,OAAO,CAAC,CAAC,OAAO,OAAO;4CACvB,cAAc,OAAO,OAAO;4CAC5B,QAAQ;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IACT,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAElD,OAAO,CAAC,CAAC,OAAO,YAAY;gDAC5B,cAAc,OAAO,YAAY;gDACjC,QAAQ;;;;;;0DAGV,8OAAC,qIAAA,CAAA,UAAK;gDACJ,IAAG;gDACH,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IACT,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAErD,OAAO,CAAC,CAAC,OAAO,eAAe;gDAC/B,cAAc,OAAO,eAAe;gDACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,8BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,qBAAqB,oBAAoB;;+CAE1C,qBACF,kBAEA;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 8410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/components/TransactionsManagement.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { toast } from \"react-toastify\";\nimport {\n  CreditCard,\n  Calendar,\n  CheckCircle,\n  Clock,\n  XCircle,\n  ArrowUpCircle,\n  ArrowDownCircle,\n} from \"lucide-react\";\nimport { Transaction } from \"@/types/account\";\nimport { formatCurrency } from \"@/utils/currencyUtils\";\n\nconst TransactionsManagement: React.FC = () => {\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState<\n    \"all\" | \"balance_add\" | \"price_request_payment\"\n  >(\"all\");\n\n  useEffect(() => {\n    fetchTransactions();\n  }, [filter]);\n\n  const fetchTransactions = async () => {\n    setLoading(true);\n    try {\n      const url =\n        filter === \"all\"\n          ? \"https://api.imdadport.com/api/transactions/\"\n          : `https://api.imdadport.com/api/transactions/?type=${filter}`;\n\n      const response = await fetch(url, {\n        headers: {\n          Authorization: `Bearer ${localStorage.getItem(\"access_token\")}`,\n        },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setTransactions(data);\n      } else {\n        toast.error(\"فشل في تحميل المعاملات\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching transactions:\", error);\n      toast.error(\"فشل في تحميل المعاملات\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTransactionIcon = (transaction: Transaction) => {\n    if (transaction.transaction_type === \"balance_add\") {\n      return <ArrowUpCircle className=\"w-5 h-5 text-green-600\" />;\n    } else {\n      return <ArrowDownCircle className=\"w-5 h-5 text-red-600\" />;\n    }\n  };\n\n  const getTransactionTypeLabel = (type: string) => {\n    switch (type) {\n      case \"balance_add\":\n        return \"إضافة رصيد\";\n      case \"price_request\":\n        return \"دفع طلب سعر\";\n      case \"price_request_payment\":\n        return \"دفع طلب سعر\";\n      default:\n        return type;\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"pending\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n            <Clock className=\"w-3 h-3 ml-1\" />\n            قيد المراجعة\n          </span>\n        );\n      case \"completed\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n            <CheckCircle className=\"w-3 h-3 ml-1\" />\n            مكتمل\n          </span>\n        );\n      case \"failed\":\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n            <XCircle className=\"w-3 h-3 ml-1\" />\n            فاشل\n          </span>\n        );\n      default:\n        return (\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            غير محدد\n          </span>\n        );\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  if (loading) {\n    return (\n      <div\n        className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\"\n        dir=\"rtl\"\n      >\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <h3 className=\"text-xl font-bold text-white\">المعاملات المالية</h3>\n        </div>\n        <div className=\"p-8 flex justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\" dir=\"rtl\">\n      {/* Transactions List */}\n      <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-gray-900 to-black px-6 py-5\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-xl font-bold text-white\">\n                سجل المعاملات المالية\n              </h3>\n              <p className=\"text-sm text-gray-300 mt-1\">\n                تتبع جميع المعاملات المالية الخاصة بك\n              </p>\n            </div>\n\n            {/* Filter */}\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <select\n                value={filter}\n                onChange={(e) =>\n                  setFilter(\n                    e.target.value as\n                      | \"all\"\n                      | \"balance_add\"\n                      | \"price_request_payment\"\n                  )\n                }\n                className=\"bg-white text-gray-900 px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-gray-500 focus:border-transparent\"\n              >\n                <option value=\"all\">جميع المعاملات</option>\n                <option value=\"balance_add\">إضافة رصيد</option>\n                <option value=\"price_request_payment\">دفع طلبات الأسعار</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {transactions.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <CreditCard className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-600 mb-2\">\n                لا توجد معاملات\n              </h3>\n              <p className=\"text-gray-500\">لم تقم بأي معاملات مالية بعد</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {transactions.map((transaction) => (\n                <div\n                  key={transaction.id}\n                  className=\"border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4 space-x-reverse flex-1\">\n                      <div className=\"flex-shrink-0\">\n                        {getTransactionIcon(transaction)}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <h4 className=\"text-lg font-semibold text-gray-900\">\n                            {getTransactionTypeLabel(\n                              transaction.transaction_type\n                            )}\n                          </h4>\n                          {getStatusBadge(transaction.status)}\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3\">\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 ml-2\" />\n                            <span>\n                              التاريخ: {formatDate(transaction.created_at)}\n                            </span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <span\n                              className={`font-semibold ${\n                                transaction.transaction_type === \"balance_add\"\n                                  ? \"text-green-600\"\n                                  : \"text-red-600\"\n                              }`}\n                            >\n                              {transaction.transaction_type === \"balance_add\"\n                                ? \"+\"\n                                : \"-\"}\n                              {(() => {\n                                const currency =\n                                  transaction.price_request?.user_currency ||\n                                  transaction.balance_request?.user_currency;\n                                return formatCurrency(\n                                  transaction.amount,\n                                  currency\n                                );\n                              })()}\n                            </span>\n                          </div>\n                        </div>\n\n                        {transaction.description_ar && (\n                          <div className=\"mb-3\">\n                            <p className=\"text-sm text-gray-600 bg-gray-50 p-2 rounded\">\n                              <span className=\"font-medium\">الوصف: </span>\n                              {transaction.description_ar}\n                            </p>\n                          </div>\n                        )}\n\n                        {transaction.processed_at && (\n                          <div className=\"text-sm text-gray-500\">\n                            <span className=\"font-medium\">\n                              تمت المعالجة في:{\" \"}\n                            </span>\n                            {formatDate(transaction.processed_at)}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TransactionsManagement;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,yBAAmC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEjC;IAEF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,MACJ,WAAW,QACP,gDACA,CAAC,iDAAiD,EAAE,QAAQ;YAElE,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACjE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,gBAAgB;YAClB,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY,gBAAgB,KAAK,eAAe;YAClD,qBAAO,8OAAC,4NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAClC,OAAO;YACL,qBAAO,8OAAC,gOAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAIxC,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI9C,KAAK;gBACH,qBACE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAI1C;gBACE,qBACE,8OAAC;oBAAK,WAAU;8BAAoG;;;;;;QAI1H;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,WAAU;YACV,KAAI;;8BAEJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAA+B;;;;;;;;;;;8BAE/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAY,KAAI;kBAE7B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAG7C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IACT,UACE,EAAE,MAAM,CAAC,KAAK;oCAMlB,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAc;;;;;;sDAC5B,8OAAC;4CAAO,OAAM;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9C,8OAAC;oBAAI,WAAU;8BACZ,aAAa,MAAM,KAAK,kBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,wBACC,YAAY,gBAAgB;;;;;;4DAG/B,eAAe,YAAY,MAAM;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;;4EAAK;4EACM,WAAW,YAAY,UAAU;;;;;;;;;;;;;0EAG/C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAW,CAAC,cAAc,EACxB,YAAY,gBAAgB,KAAK,gBAC7B,mBACA,gBACJ;;wEAED,YAAY,gBAAgB,KAAK,gBAC9B,MACA;wEACH,CAAC;4EACA,MAAM,WACJ,YAAY,aAAa,EAAE,iBAC3B,YAAY,eAAe,EAAE;4EAC/B,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAClB,YAAY,MAAM,EAClB;wEAEJ,CAAC;;;;;;;;;;;;;;;;;;oDAKN,YAAY,cAAc,kBACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;oEAAK,WAAU;8EAAc;;;;;;gEAC7B,YAAY,cAAc;;;;;;;;;;;;oDAKhC,YAAY,YAAY,kBACvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEAAc;oEACX;;;;;;;4DAElB,WAAW,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;+BAhEzC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ErC;uCAEe", "debugId": null}}, {"offset": {"line": 8921, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/IMPORTANT/work/export/front/src/app/account/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\nimport { toast } from \"react-toastify\";\nimport useTranslation from \"@/hooks/useTranslation\";\nimport { Menu, X } from \"lucide-react\";\n\n// Import components\nimport Sidebar from \"./components/Sidebar\";\nimport ProfileForm from \"./components/ProfileForm\";\nimport PasswordForm from \"./components/PasswordForm\";\nimport AdminPanel from \"./components/AdminPanel\";\nimport BalanceForm from \"./components/BalanceForm\";\nimport PriceRequestForm from \"./components/PriceRequestForm\";\nimport PriceRequestsManagement from \"./components/PriceRequestsManagement\";\nimport BalanceRequestsManagement from \"./components/BalanceRequestsManagement\";\nimport SummaryCards from \"./components/SummaryCards\";\n\n// Import services and utilities\nimport {\n  getUserProfile,\n  deleteAccount,\n  logout,\n  changePassword,\n} from \"@/services/authService\";\nimport {\n  extractPhoneNumber,\n  getCountryCodeFromName,\n} from \"@/utils/accountUtils\";\nimport {\n  isEmailVerificationError,\n  triggerEmailVerificationPopup,\n} from \"@/utils/navigationUtils\";\nimport { ProfileFormData, User } from \"@/types/account\";\nimport { ChildData } from \"@/types/auth\";\nimport { isProfileComplete, isAddressComplete } from \"@/utils/addressUtils\";\nimport AddressPage from \"./components/Address\";\nimport TransactionsManagement from \"./components/TransactionsManagement\";\n\n// Extended User interface to handle both phone and phone_number fields\nexport interface ExtendedUser extends Omit<User, \"children\"> {\n  phone: string;\n  phone_number?: string;\n  children: ChildData[];\n  governorate?: string;\n  city?: string;\n  other_relation?: string;\n  address?: string;\n  zipcode?: string;\n  relation?: string;\n  is_email_verified?: boolean;\n  nearest_airport?: string;\n  nearest_port?: string;\n  balance?: number;\n  user_currency?: string;\n  is_staff: boolean;\n  is_superuser: boolean;\n  is_active: boolean;\n  date_joined: string;\n  last_login: string;\n}\n\nconst AccountPage = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { t } = useTranslation();\n  const [isAuthenticating, setIsAuthenticating] = useState(true);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n\n  // Check authentication on component mount\n  useEffect(() => {\n    const token = localStorage.getItem(\"access_token\");\n    if (!token) {\n      // Add a small delay to show loading state\n      setTimeout(() => {\n        router.push(\"/login\");\n      }, 1000);\n      return;\n    }\n    setIsAuthenticating(false);\n  }, [router]);\n\n  // Get the last active tab from localStorage or default to \"profile\"\n  const [activeTab, setActiveTab] = useState(() => {\n    if (typeof window !== \"undefined\") {\n      return localStorage.getItem(\"userAccountActiveTab\") || \"profile\";\n    }\n    return \"profile\";\n  });\n\n  // User state\n  const [user, setUser] = useState<ExtendedUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState<ProfileFormData>({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    phoneCountry: \"EG\",\n    phone: \"\",\n    countryCode: \"EG\",\n    governorate: \"\",\n    city: \"\",\n    relation: \"\",\n    otherRelation: \"\",\n    children: [],\n  });\n\n  // Delete account state\n  const [deletePassword, setDeletePassword] = useState<string>(\"\");\n  const [updateLoading, setUpdateLoading] = useState(false);\n\n  // Handle URL parameters for tab selection\n  useEffect(() => {\n    const tab = searchParams.get(\"tab\");\n    if (tab) {\n      setActiveTab(tab);\n    }\n  }, [searchParams]);\n\n  // Fetch user profile on component mount\n  useEffect(() => {\n    fetchUserProfile();\n  }, []);\n\n  // Check admin status\n\n  // Save active tab to localStorage when changed\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      localStorage.setItem(\"userAccountActiveTab\", activeTab);\n    }\n  }, [activeTab]);\n\n  // Fetch user profile from API\n  const fetchUserProfile = async () => {\n    setLoading(true);\n    try {\n      const data = await getUserProfile();\n\n      // Extract phone number without country code if it exists\n      let phoneNumber = \"\";\n      let phoneCountry = \"EG\"; // Default to Egypt\n\n      // Check both phone and phone_number fields\n      const phoneValue = data.phone_number;\n\n      if (phoneValue) {\n        const { phoneNumber: extractedPhone, countryCode } =\n          extractPhoneNumber(phoneValue);\n\n        phoneNumber = extractedPhone;\n        phoneCountry = countryCode;\n        console.log(\n          \"Extracted phone:\",\n          phoneNumber,\n          \"Country:\",\n          phoneCountry,\n          \"From:\",\n          phoneValue\n        );\n      } else {\n        console.warn(\"No phone number found in user data:\", data);\n      }\n\n      // Log the full user data for debugging\n      console.log(\"User data:\", data);\n\n      // Get country code from country name if it exists\n      let countryCode = \"EG\"; // Default to Egypt\n      if (\n        data &&\n        typeof data === \"object\" &&\n        \"country\" in data &&\n        data.country\n      ) {\n        countryCode = getCountryCodeFromName(data.country);\n      }\n\n      // Convert Child[] to ChildData[] by ensuring id is string\n\n      const extendedUser: ExtendedUser = {\n        ...data,\n        phone: phoneNumber,\n        phone_number: phoneNumber,\n        governorate: data.governorate,\n        city: data.city,\n        other_relation:\n          (data as { other_relation?: string }).other_relation || \"\",\n        address: data.address,\n        zipcode: data.zipcode,\n        relation: (data as { relation?: string }).relation || \"\",\n        is_email_verified: (data as { is_email_verified?: boolean })\n          .is_email_verified,\n        nearest_airport: (data as { nearest_airport?: string }).nearest_airport,\n        nearest_port: (data as { nearest_port?: string }).nearest_port,\n        children: Array.isArray(\n          (\n            data as {\n              children?: {\n                id: number;\n                name: string;\n                class: string;\n                stage: string;\n              }[];\n            }\n          ).children\n        )\n          ? (\n              data as {\n                children: {\n                  id: number;\n                  name: string;\n                  class: string;\n                  stage: string;\n                }[];\n              }\n            ).children.map((child) => ({ ...child, id: String(child.id) }))\n          : [],\n        balance: (data as { balance?: number }).balance,\n        user_currency: (data as { user_currency?: string }).user_currency,\n        is_staff: (data as { is_staff?: boolean }).is_staff ?? false,\n        is_superuser:\n          (data as { is_superuser?: boolean }).is_superuser ?? false,\n        is_active: (data as { is_active?: boolean }).is_active ?? false,\n        date_joined: data.date_joined || \"\",\n        last_login: data.last_login || \"\",\n      };\n\n      setUser(extendedUser);\n\n      // Update profile form with user data\n      const updatedFormData: ProfileFormData = {\n        firstName: data.first_name || \"\",\n        lastName: data.last_name || \"\",\n        email: data.email || \"\",\n        phoneCountry: phoneCountry,\n        phone: phoneNumber,\n        countryCode: countryCode,\n        governorate: data.governorate || \"\",\n        city: data.city || \"\",\n        relation: (data as { relation?: string }).relation || \"\",\n        otherRelation:\n          (data as { other_relation?: string }).other_relation || \"\",\n        children: Array.isArray(\n          (\n            data as {\n              children?: {\n                id: number;\n                name: string;\n                class: string;\n                stage: string;\n              }[];\n            }\n          ).children\n        )\n          ? (\n              data as {\n                children: {\n                  id: number;\n                  name: string;\n                  class: string;\n                  stage: string;\n                }[];\n              }\n            ).children.map((child) => ({ ...child, id: String(child.id) }))\n          : [],\n      };\n\n      setFormData(updatedFormData);\n      setLoading(false);\n    } catch (error: unknown) {\n      console.error(\"Error fetching user profile:\", error);\n      setError(\"Failed to load your profile. Please try again later.\");\n      setLoading(false);\n    }\n  };\n\n  // Handle user update from ProfileForm component\n  const handleUserUpdate = (updatedUser: ExtendedUser) => {\n    setUser(updatedUser);\n\n    // Update form data to reflect the changes\n    const updatedFormData: ProfileFormData = {\n      firstName: updatedUser.first_name || \"\",\n      lastName: updatedUser.last_name || \"\",\n      email: updatedUser.email || \"\",\n      phoneCountry: formData.phoneCountry, // Keep existing phone country\n      phone: formData.phone, // Keep existing phone format\n      relation: updatedUser.relation || \"\",\n      otherRelation: updatedUser.other_relation || \"\",\n      children: updatedUser.children || [],\n      countryCode: formData.countryCode, // Keep existing country code\n      governorate: updatedUser.governorate || \"\",\n      city: updatedUser.city || \"\",\n    };\n    setFormData(updatedFormData);\n  };\n\n  // Handle password update\n  const handlePasswordUpdate = async (data: {\n    currentPassword: string;\n    newPassword: string;\n  }) => {\n    setUpdateLoading(true);\n    try {\n      await changePassword(data.currentPassword, data.newPassword);\n\n      setUpdateLoading(false);\n\n      // Show success message with a custom configuration\n      toast.success(t(\"messages.success.passwordChanged\"), {\n        autoClose: 1500, // Close after 1.5 seconds\n        hideProgressBar: true, // Remove timer bar\n        closeOnClick: true,\n        pauseOnHover: false,\n        draggable: true,\n      });\n\n      // Refresh page after 1.5 seconds\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error: unknown) {\n      setUpdateLoading(false);\n      console.error(\"Error updating password:\", error);\n\n      // Check if this is an email verification error\n      if (isEmailVerificationError(error)) {\n        triggerEmailVerificationPopup(error);\n        return;\n      }\n\n      toast.error(\"Failed to update password\");\n    }\n  };\n\n  // Handle delete account dialog\n  const handleDeleteAccount = async (password: FormData) => {\n    setUpdateLoading(true);\n    try {\n      // Extract the password from the FormData\n      const passwordValue = password.get(\"password\") as string;\n\n      // Call the deleteAccount function with the password string\n      await deleteAccount(passwordValue);\n\n      // Logout and redirect to login page\n      toast.success(\"Account deleted successfully\");\n\n      logout();\n      router.push(\"/login\");\n    } catch (error: unknown) {\n      console.error(\"Error deleting account:\", error);\n      toast.error(\"Failed to delete account\");\n      setUpdateLoading(false);\n    }\n  };\n\n  // Prevent form resubmission on page refresh\n  useEffect(() => {\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n      if (updateLoading) {\n        // Cancel the event\n        e.preventDefault();\n        // Chrome requires returnValue to be set\n        e.returnValue = \"\";\n      }\n    };\n\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    };\n  }, [updateLoading]);\n  // Render content based on active tab\n  const renderContent = () => {\n    switch (activeTab) {\n      case \"profile\":\n        return (\n          <ProfileForm\n            initialFormData={formData}\n            user={user}\n            onUserUpdate={handleUserUpdate}\n          />\n        );\n      case \"address\":\n        // Redirect to address page\n        return <AddressPage />;\n      case \"admin\":\n        return <AdminPanel currentUser={user} />;\n      case \"balance\":\n        return <BalanceForm user={user} />;\n      case \"balance-requests\":\n        return <BalanceRequestsManagement />;\n      case \"price-request\":\n        return <PriceRequestForm />;\n      case \"price-requests\":\n        return <PriceRequestsManagement />;\n      case \"transactions\":\n        return <TransactionsManagement />;\n      case \"password\":\n        return (\n          <PasswordForm\n            onSubmit={handlePasswordUpdate}\n            loading={updateLoading}\n          />\n        );\n\n      case \"settings\":\n        return (\n          <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden\">\n            {/* Header */}\n            <div className=\"bg-black px-6 py-4\">\n              <h3 className=\"text-xl font-bold text-white\">إعدادات الحساب</h3>\n              <p className=\"text-sm text-gray-100 mt-1\">إدارة تفضيلات حسابك</p>\n            </div>\n\n            <div className=\"p-6\">\n              {/* Admin Panel Link */}\n              {user && user.is_email_verified && (\n                <div className=\"border border-gray-200 rounded-xl overflow-hidden mb-6\">\n                  <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n                    <h4 className=\"text-lg font-semibold text-gray-800\">\n                      لوحة الإدارة\n                    </h4>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      الوصول إلى الميزات الإدارية\n                    </p>\n                  </div>\n\n                  <div className=\"p-6\">\n                    <p className=\"text-gray-700 mb-4\">\n                      لديك صلاحيات إدارية. ادخل إلى لوحة الإدارة لإدارة\n                      المستخدمين والتصاريح.\n                    </p>\n                    <button\n                      onClick={() => router.push(\"/admin\")}\n                      className=\"btn-nursery\"\n                    >\n                      الذهاب إلى لوحة الإدارة\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Delete Account Section */}\n              <div className=\"border border-red-200 rounded-xl overflow-hidden\">\n                <div className=\"bg-red-50 px-6 py-4 border-b border-red-200\">\n                  <h4 className=\"text-lg font-semibold text-red-800\">\n                    منطقة الخطر\n                  </h4>\n                  <p className=\"text-sm text-red-600 mt-1\">\n                    إجراءات لا يمكن التراجع عنها تؤثر على حسابك\n                  </p>\n                </div>\n\n                <div className=\"p-6\">\n                  <h5 className=\"text-lg font-semibold text-red-700 mb-3\">\n                    حذف الحساب\n                  </h5>\n                  <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\">\n                    <p className=\"text-red-700 text-sm font-medium\">\n                      ⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع\n                      بياناتك بشكل دائم.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <input\n                      type=\"password\"\n                      value={deletePassword}\n                      onChange={(e) => setDeletePassword(e.target.value)}\n                      placeholder=\"أدخل كلمة المرور لتأكيد الحذف\"\n                      className=\"w-full py-3 px-4 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:border-red-500\"\n                    />\n                    <button\n                      onClick={() => {\n                        const formData = new FormData();\n                        formData.append(\"password\", deletePassword);\n                        handleDeleteAccount(formData);\n                      }}\n                      disabled={!deletePassword || updateLoading}\n                      className=\"w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-base font-semibold text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {updateLoading ? \"جاري حذف الحساب...\" : \"حذف الحساب\"}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  // Navigation handler for sidebar\n  const handleNavigation = (tab: string) => {\n    setActiveTab(tab);\n    localStorage.setItem(\"userAccountActiveTab\", tab);\n  };\n\n  // If still checking authentication, show loading state\n  if (isAuthenticating) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"spinner-nursery mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">{t(\"common.loading\")}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 px-4 sm:px-6 lg:px-8\"\n      dir=\"rtl\"\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Page Header */}\n        <div>\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-2\">\n              حسابي\n            </h1>\n            <p className=\"text-gray-600 text-lg\">\n              إدارة معلوماتك الشخصية وإعدادات حسابك\n            </p>\n          </div>\n\n          {/* Summary Cards */}\n          <SummaryCards className=\"mb-8\" userCurrency={user?.user_currency} />\n        </div>\n\n        {/* Email Verification Notification */}\n        {user && !user.is_email_verified && (\n          <div className=\"mb-8\">\n            <div\n              className=\"bg-red-50 border border-red-200 rounded-xl p-6\"\n              dir=\"rtl\"\n            >\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-red-400 rounded-full flex items-center justify-center\">\n                    <span className=\"text-red-800 font-bold\">✉</span>\n                  </div>\n                </div>\n                <div className=\"mr-3 flex-1\">\n                  <h3 className=\"text-lg font-semibold text-red-800 mb-2\">\n                    تأكيد البريد الإلكتروني مطلوب\n                  </h3>\n                  <p className=\"text-red-700 mb-4\">\n                    يرجى تأكيد بريدك الإلكتروني لتتمكن من استخدام جميع ميزات\n                    الموقع بشكل كامل.\n                  </p>\n                  <div className=\"mt-4\">\n                    <button\n                      onClick={() =>\n                        router.push(\n                          `/activate-account?email=${encodeURIComponent(\n                            user.email\n                          )}`\n                        )\n                      }\n                      className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium\"\n                    >\n                      تأكيد البريد الإلكتروني\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Profile Completion Message */}\n        {user && !isProfileComplete(user) && (\n          <div className=\"mb-8\">\n            <div\n              className=\"bg-yellow-50 border border-yellow-200 rounded-xl p-6\"\n              dir=\"rtl\"\n            >\n              <div className=\"flex items-start\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center\">\n                    <span className=\"text-yellow-800 font-bold\">!</span>\n                  </div>\n                </div>\n                <div className=\"mr-3 flex-1\">\n                  <h3 className=\"text-lg font-semibold text-yellow-800 mb-2\">\n                    أكمل ملفك الشخصي\n                  </h3>\n                  <p className=\"text-yellow-700 mb-4\">\n                    يرجى إكمال جميع معلومات ملفك الشخصي لتتمكن من استخدام جميع\n                    ميزات الموقع.\n                  </p>\n                  <div className=\"space-y-2\">\n                    {(!user.first_name || !user.last_name) && (\n                      <p className=\"text-sm text-yellow-600\">\n                        • أكمل المعلومات الأساسية\n                      </p>\n                    )}\n                    {!user.phone_number && (\n                      <p className=\"text-sm text-yellow-600\">\n                        • أضف رقم الهاتف\n                      </p>\n                    )}\n                    {!isAddressComplete(user) && (\n                      <p className=\"text-sm text-yellow-600\">\n                        • أضف معلومات العنوان\n                      </p>\n                    )}\n                  </div>\n                  <div className=\"mt-4 flex flex-wrap gap-3\">\n                    {(!user.first_name ||\n                      !user.last_name ||\n                      !user.phone_number) && (\n                      <button\n                        onClick={() => handleNavigation(\"profile\")}\n                        className=\"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm font-medium\"\n                      >\n                        تحديث الملف الشخصي\n                      </button>\n                    )}\n                    {!isAddressComplete(user) && (\n                      <button\n                        onClick={() => handleNavigation(\"address\")}\n                        className=\"px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium\"\n                      >\n                        إضافة العنوان\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Mobile Menu Button */}\n          <div className=\"lg:hidden\">\n            <button\n              onClick={() => setIsSidebarOpen(!isSidebarOpen)}\n              className=\"w-full flex items-center justify-center px-4 py-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors\"\n            >\n              {isSidebarOpen ? (\n                <X className=\"w-5 h-5 ml-2\" />\n              ) : (\n                <Menu className=\"w-5 h-5 ml-2\" />\n              )}\n              <span>{isSidebarOpen ? \"إغلاق القائمة\" : \"فتح القائمة\"}</span>\n            </button>\n          </div>\n\n          {/* Mobile Sidebar */}\n          <div\n            className={`\n              lg:hidden\n              ${isSidebarOpen ? \"block\" : \"hidden\"}\n              w-full mb-4\n            `}\n          >\n            <Sidebar\n              activeTab={activeTab}\n              onTabChange={(tab) => {\n                setActiveTab(tab);\n                setIsSidebarOpen(false);\n              }}\n            />\n          </div>\n\n          {/* Desktop Sidebar */}\n          <div className=\"hidden lg:block lg:w-80 shrink-0\">\n            <Sidebar\n              activeTab={activeTab}\n              onTabChange={(tab) => {\n                setActiveTab(tab);\n                setIsSidebarOpen(false);\n              }}\n            />\n          </div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {loading ? (\n              <div className=\"card-nursery p-8\">\n                <div className=\"flex items-center justify-center h-64\">\n                  <div className=\"text-center\">\n                    <div className=\"spinner-nursery mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">{t(\"common.loading\")}</p>\n                  </div>\n                </div>\n              </div>\n            ) : error ? (\n              <div className=\"card-nursery p-8\">\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                  <div className=\"text-red-700 font-medium\">{error}</div>\n                </div>\n              </div>\n            ) : (\n              renderContent()\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,gCAAgC;AAChC;AAMA;AAIA;AAMA;AACA;AACA;AAtCA;;;;;;;;;;;;;;;;;;;;;;AA+DA,MAAM,cAAc;IAClB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAc,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,0CAA0C;YAC1C,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;YACH;QACF;QACA,oBAAoB;IACtB,GAAG;QAAC;KAAO;IAEX,oEAAoE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,WAAW;QACX,UAAU;QACV,OAAO;QACP,cAAc;QACd,OAAO;QACP,aAAa;QACb,aAAa;QACb,MAAM;QACN,UAAU;QACV,eAAe;QACf,UAAU,EAAE;IACd;IAEA,uBAAuB;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,KAAK;YACP,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBAAqB;IAErB,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAEnC;IACF,GAAG;QAAC;KAAU;IAEd,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;YAEhC,yDAAyD;YACzD,IAAI,cAAc;YAClB,IAAI,eAAe,MAAM,mBAAmB;YAE5C,2CAA2C;YAC3C,MAAM,aAAa,KAAK,YAAY;YAEpC,IAAI,YAAY;gBACd,MAAM,EAAE,aAAa,cAAc,EAAE,WAAW,EAAE,GAChD,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;gBAErB,cAAc;gBACd,eAAe;gBACf,QAAQ,GAAG,CACT,oBACA,aACA,YACA,cACA,SACA;YAEJ,OAAO;gBACL,QAAQ,IAAI,CAAC,uCAAuC;YACtD;YAEA,uCAAuC;YACvC,QAAQ,GAAG,CAAC,cAAc;YAE1B,kDAAkD;YAClD,IAAI,cAAc,MAAM,mBAAmB;YAC3C,IACE,QACA,OAAO,SAAS,YAChB,aAAa,QACb,KAAK,OAAO,EACZ;gBACA,cAAc,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,OAAO;YACnD;YAEA,0DAA0D;YAE1D,MAAM,eAA6B;gBACjC,GAAG,IAAI;gBACP,OAAO;gBACP,cAAc;gBACd,aAAa,KAAK,WAAW;gBAC7B,MAAM,KAAK,IAAI;gBACf,gBACE,AAAC,KAAqC,cAAc,IAAI;gBAC1D,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;gBACrB,UAAU,AAAC,KAA+B,QAAQ,IAAI;gBACtD,mBAAmB,AAAC,KACjB,iBAAiB;gBACpB,iBAAiB,AAAC,KAAsC,eAAe;gBACvE,cAAc,AAAC,KAAmC,YAAY;gBAC9D,UAAU,MAAM,OAAO,CACrB,AACE,KAQA,QAAQ,IAER,AACE,KAQA,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAU,CAAC;wBAAE,GAAG,KAAK;wBAAE,IAAI,OAAO,MAAM,EAAE;oBAAE,CAAC,KAC7D,EAAE;gBACN,SAAS,AAAC,KAA8B,OAAO;gBAC/C,eAAe,AAAC,KAAoC,aAAa;gBACjE,UAAU,AAAC,KAAgC,QAAQ,IAAI;gBACvD,cACE,AAAC,KAAoC,YAAY,IAAI;gBACvD,WAAW,AAAC,KAAiC,SAAS,IAAI;gBAC1D,aAAa,KAAK,WAAW,IAAI;gBACjC,YAAY,KAAK,UAAU,IAAI;YACjC;YAEA,QAAQ;YAER,qCAAqC;YACrC,MAAM,kBAAmC;gBACvC,WAAW,KAAK,UAAU,IAAI;gBAC9B,UAAU,KAAK,SAAS,IAAI;gBAC5B,OAAO,KAAK,KAAK,IAAI;gBACrB,cAAc;gBACd,OAAO;gBACP,aAAa;gBACb,aAAa,KAAK,WAAW,IAAI;gBACjC,MAAM,KAAK,IAAI,IAAI;gBACnB,UAAU,AAAC,KAA+B,QAAQ,IAAI;gBACtD,eACE,AAAC,KAAqC,cAAc,IAAI;gBAC1D,UAAU,MAAM,OAAO,CACrB,AACE,KAQA,QAAQ,IAER,AACE,KAQA,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAU,CAAC;wBAAE,GAAG,KAAK;wBAAE,IAAI,OAAO,MAAM,EAAE;oBAAE,CAAC,KAC7D,EAAE;YACR;YAEA,YAAY;YACZ,WAAW;QACb,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;YACT,WAAW;QACb;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,CAAC;QACxB,QAAQ;QAER,0CAA0C;QAC1C,MAAM,kBAAmC;YACvC,WAAW,YAAY,UAAU,IAAI;YACrC,UAAU,YAAY,SAAS,IAAI;YACnC,OAAO,YAAY,KAAK,IAAI;YAC5B,cAAc,SAAS,YAAY;YACnC,OAAO,SAAS,KAAK;YACrB,UAAU,YAAY,QAAQ,IAAI;YAClC,eAAe,YAAY,cAAc,IAAI;YAC7C,UAAU,YAAY,QAAQ,IAAI,EAAE;YACpC,aAAa,SAAS,WAAW;YACjC,aAAa,YAAY,WAAW,IAAI;YACxC,MAAM,YAAY,IAAI,IAAI;QAC5B;QACA,YAAY;IACd;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,OAAO;QAIlC,iBAAiB;QACjB,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,eAAe,EAAE,KAAK,WAAW;YAE3D,iBAAiB;YAEjB,mDAAmD;YACnD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,EAAE,qCAAqC;gBACnD,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,iCAAiC;YACjC,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,EAAE,OAAO,OAAgB;YACvB,iBAAiB;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,+CAA+C;YAC/C,IAAI,CAAA,GAAA,+HAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE;gBAC9B;YACF;YAEA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,+BAA+B;IAC/B,MAAM,sBAAsB,OAAO;QACjC,iBAAiB;QACjB,IAAI;YACF,yCAAyC;YACzC,MAAM,gBAAgB,SAAS,GAAG,CAAC;YAEnC,2DAA2D;YAC3D,MAAM,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE;YAEpB,oCAAoC;YACpC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;YACL,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,iBAAiB;QACnB;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,eAAe;gBACjB,mBAAmB;gBACnB,EAAE,cAAc;gBAChB,wCAAwC;gBACxC,EAAE,WAAW,GAAG;YAClB;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG;QAAC;KAAc;IAClB,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,mJAAA,CAAA,UAAW;oBACV,iBAAiB;oBACjB,MAAM;oBACN,cAAc;;;;;;YAGpB,KAAK;gBACH,2BAA2B;gBAC3B,qBAAO,8OAAC,+IAAA,CAAA,UAAW;;;;;YACrB,KAAK;gBACH,qBAAO,8OAAC,kJAAA,CAAA,UAAU;oBAAC,aAAa;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAW;oBAAC,MAAM;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,iKAAA,CAAA,UAAyB;;;;;YACnC,KAAK;gBACH,qBAAO,8OAAC,wJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,+JAAA,CAAA,UAAuB;;;;;YACjC,KAAK;gBACH,qBAAO,8OAAC,8JAAA,CAAA,UAAsB;;;;;YAChC,KAAK;gBACH,qBACE,8OAAC,oJAAA,CAAA,UAAY;oBACX,UAAU;oBACV,SAAS;;;;;;YAIf,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;;gCAEZ,QAAQ,KAAK,iBAAiB,kBAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAIlC,8OAAC;oDACC,SAAS,IAAM,OAAO,IAAI,CAAC;oDAC3B,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAQP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAGnD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DAGxD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;;;;;;8DAMlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,aAAY;4DACZ,WAAU;;;;;;sEAEZ,8OAAC;4DACC,SAAS;gEACP,MAAM,WAAW,IAAI;gEACrB,SAAS,MAAM,CAAC,YAAY;gEAC5B,oBAAoB;4DACtB;4DACA,UAAU,CAAC,kBAAkB;4DAC7B,WAAU;sEAET,gBAAgB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxD;gBACE,OAAO;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,mBAAmB,CAAC;QACxB,aAAa;QACb,aAAa,OAAO,CAAC,wBAAwB;IAC/C;IAEA,uDAAuD;IACvD,IAAI,kBAAkB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAiB,EAAE;;;;;;;;;;;;;;;;;IAIxC;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,KAAI;kBAEJ,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC,oJAAA,CAAA,UAAY;4BAAC,WAAU;4BAAO,cAAc,MAAM;;;;;;;;;;;;gBAIpD,QAAQ,CAAC,KAAK,iBAAiB,kBAC9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,KAAI;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0C;;;;;;sDAGxD,8OAAC;4CAAE,WAAU;sDAAoB;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS,IACP,OAAO,IAAI,CACT,CAAC,wBAAwB,EAAE,mBACzB,KAAK,KAAK,GACT;gDAGP,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWZ,QAAQ,CAAC,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,uBAC1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,KAAI;kCAEJ,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,8OAAC;4CAAE,WAAU;sDAAuB;;;;;;sDAIpC,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,SAAS,mBACnC,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;gDAIxC,CAAC,KAAK,YAAY,kBACjB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;gDAIxC,CAAC,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,uBAClB,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,CAAC,KAAK,UAAU,IAChB,CAAC,KAAK,SAAS,IACf,CAAC,KAAK,YAAY,mBAClB,8OAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;gDAIF,CAAC,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,uBAClB,8OAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWf,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,iBAAiB,CAAC;gCACjC,WAAU;;oCAET,8BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;kDAAM,gBAAgB,kBAAkB;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BACC,WAAW,CAAC;;cAEV,EAAE,gBAAgB,UAAU,SAAS;;YAEvC,CAAC;sCAED,cAAA,8OAAC,+IAAA,CAAA,UAAO;gCACN,WAAW;gCACX,aAAa,CAAC;oCACZ,aAAa;oCACb,iBAAiB;gCACnB;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,+IAAA,CAAA,UAAO;gCACN,WAAW;gCACX,aAAa,CAAC;oCACZ,aAAa;oCACb,iBAAiB;gCACnB;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;uCAIpC,sBACF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;;;;;;;;;;uCAI/C;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}]}