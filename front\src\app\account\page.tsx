"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import useTranslation from "@/hooks/useTranslation";
import { Menu, X } from "lucide-react";

// Import components
import Sidebar from "./components/Sidebar";
import ProfileForm from "./components/ProfileForm";
import PasswordForm from "./components/PasswordForm";
import AdminPanel from "./components/AdminPanel";
import BalanceForm from "./components/BalanceForm";
import PriceRequestForm from "./components/PriceRequestForm";
import PriceRequestsManagement from "./components/PriceRequestsManagement";
import BalanceRequestsManagement from "./components/BalanceRequestsManagement";
import SummaryCards from "./components/SummaryCards";

// Import services and utilities
import {
  getUserProfile,
  deleteAccount,
  logout,
  changePassword,
} from "@/services/authService";
import {
  extractPhoneNumber,
  getCountryCodeFromName,
} from "@/utils/accountUtils";
import {
  isEmailVerificationError,
  triggerEmailVerificationPopup,
} from "@/utils/navigationUtils";
import { ProfileFormData, User } from "@/types/account";
import { ChildData } from "@/types/auth";
import { isProfileComplete, isAddressComplete } from "@/utils/addressUtils";
import AddressPage from "./components/Address";
import TransactionsManagement from "./components/TransactionsManagement";

// Extended User interface to handle both phone and phone_number fields
export interface ExtendedUser extends Omit<User, "children"> {
  phone: string;
  phone_number?: string;
  children: ChildData[];
  governorate?: string;
  city?: string;
  other_relation?: string;
  address?: string;
  zipcode?: string;
  relation?: string;
  is_email_verified?: boolean;
  nearest_airport?: string;
  nearest_port?: string;
  balance?: number;
  user_currency?: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active: boolean;
  date_joined: string;
  last_login: string;
}

const AccountPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation();
  const [isAuthenticating, setIsAuthenticating] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Check authentication on component mount
  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (!token) {
      // Add a small delay to show loading state
      setTimeout(() => {
        router.push("/login");
      }, 1000);
      return;
    }
    setIsAuthenticating(false);
  }, [router]);

  // Get the last active tab from localStorage or default to "profile"
  const [activeTab, setActiveTab] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("userAccountActiveTab") || "profile";
    }
    return "profile";
  });

  // User state
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phoneCountry: "EG",
    phone: "",
    countryCode: "EG",
    governorate: "",
    city: "",
    relation: "",
    otherRelation: "",
    children: [],
  });

  // Delete account state
  const [deletePassword, setDeletePassword] = useState<string>("");
  const [updateLoading, setUpdateLoading] = useState(false);

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Fetch user profile on component mount
  useEffect(() => {
    fetchUserProfile();
  }, []);

  // Check admin status

  // Save active tab to localStorage when changed
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("userAccountActiveTab", activeTab);
    }
  }, [activeTab]);

  // Fetch user profile from API
  const fetchUserProfile = async () => {
    setLoading(true);
    try {
      const data = await getUserProfile();

      // Extract phone number without country code if it exists
      let phoneNumber = "";
      let phoneCountry = "EG"; // Default to Egypt

      // Check both phone and phone_number fields
      const phoneValue = data.phone_number;

      if (phoneValue) {
        const { phoneNumber: extractedPhone, countryCode } =
          extractPhoneNumber(phoneValue);

        phoneNumber = extractedPhone;
        phoneCountry = countryCode;
        console.log(
          "Extracted phone:",
          phoneNumber,
          "Country:",
          phoneCountry,
          "From:",
          phoneValue
        );
      } else {
        console.warn("No phone number found in user data:", data);
      }

      // Log the full user data for debugging
      console.log("User data:", data);

      // Get country code from country name if it exists
      let countryCode = "EG"; // Default to Egypt
      if (
        data &&
        typeof data === "object" &&
        "country" in data &&
        data.country
      ) {
        countryCode = getCountryCodeFromName(data.country);
      }

      // Convert Child[] to ChildData[] by ensuring id is string

      const extendedUser: ExtendedUser = {
        ...data,
        phone: phoneNumber,
        phone_number: phoneNumber,
        governorate: data.governorate,
        city: data.city,
        other_relation:
          (data as { other_relation?: string }).other_relation || "",
        address: data.address,
        zipcode: data.zipcode,
        relation: (data as { relation?: string }).relation || "",
        is_email_verified: (data as { is_email_verified?: boolean })
          .is_email_verified,
        nearest_airport: (data as { nearest_airport?: string }).nearest_airport,
        nearest_port: (data as { nearest_port?: string }).nearest_port,
        children: Array.isArray(
          (
            data as {
              children?: {
                id: number;
                name: string;
                class: string;
                stage: string;
              }[];
            }
          ).children
        )
          ? (
              data as {
                children: {
                  id: number;
                  name: string;
                  class: string;
                  stage: string;
                }[];
              }
            ).children.map((child) => ({ ...child, id: String(child.id) }))
          : [],
        balance: (data as { balance?: number }).balance,
        user_currency: (data as { user_currency?: string }).user_currency,
        is_staff: (data as { is_staff?: boolean }).is_staff ?? false,
        is_superuser:
          (data as { is_superuser?: boolean }).is_superuser ?? false,
        is_active: (data as { is_active?: boolean }).is_active ?? false,
        date_joined: data.date_joined || "",
        last_login: data.last_login || "",
      };

      setUser(extendedUser);

      // Update profile form with user data
      const updatedFormData: ProfileFormData = {
        firstName: data.first_name || "",
        lastName: data.last_name || "",
        email: data.email || "",
        phoneCountry: phoneCountry,
        phone: phoneNumber,
        countryCode: countryCode,
        governorate: data.governorate || "",
        city: data.city || "",
        relation: (data as { relation?: string }).relation || "",
        otherRelation:
          (data as { other_relation?: string }).other_relation || "",
        children: Array.isArray(
          (
            data as {
              children?: {
                id: number;
                name: string;
                class: string;
                stage: string;
              }[];
            }
          ).children
        )
          ? (
              data as {
                children: {
                  id: number;
                  name: string;
                  class: string;
                  stage: string;
                }[];
              }
            ).children.map((child) => ({ ...child, id: String(child.id) }))
          : [],
      };

      setFormData(updatedFormData);
      setLoading(false);
    } catch (error: unknown) {
      console.error("Error fetching user profile:", error);
      setError("Failed to load your profile. Please try again later.");
      setLoading(false);
    }
  };

  // Handle user update from ProfileForm component
  const handleUserUpdate = (updatedUser: ExtendedUser) => {
    setUser(updatedUser);

    // Update form data to reflect the changes
    const updatedFormData: ProfileFormData = {
      firstName: updatedUser.first_name || "",
      lastName: updatedUser.last_name || "",
      email: updatedUser.email || "",
      phoneCountry: formData.phoneCountry, // Keep existing phone country
      phone: formData.phone, // Keep existing phone format
      relation: updatedUser.relation || "",
      otherRelation: updatedUser.other_relation || "",
      children: updatedUser.children || [],
      countryCode: formData.countryCode, // Keep existing country code
      governorate: updatedUser.governorate || "",
      city: updatedUser.city || "",
    };
    setFormData(updatedFormData);
  };

  // Handle password update
  const handlePasswordUpdate = async (data: {
    currentPassword: string;
    newPassword: string;
  }) => {
    setUpdateLoading(true);
    try {
      await changePassword(data.currentPassword, data.newPassword);

      setUpdateLoading(false);

      // Show success message with a custom configuration
      toast.success(t("messages.success.passwordChanged"), {
        autoClose: 1500, // Close after 1.5 seconds
        hideProgressBar: true, // Remove timer bar
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
      });

      // Refresh page after 1.5 seconds
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error: unknown) {
      setUpdateLoading(false);
      console.error("Error updating password:", error);

      // Check if this is an email verification error
      if (isEmailVerificationError(error)) {
        triggerEmailVerificationPopup(error);
        return;
      }

      toast.error("Failed to update password");
    }
  };

  // Handle delete account dialog
  const handleDeleteAccount = async (password: FormData) => {
    setUpdateLoading(true);
    try {
      // Extract the password from the FormData
      const passwordValue = password.get("password") as string;

      // Call the deleteAccount function with the password string
      await deleteAccount(passwordValue);

      // Logout and redirect to login page
      toast.success("Account deleted successfully");

      logout();
      router.push("/login");
    } catch (error: unknown) {
      console.error("Error deleting account:", error);
      toast.error("Failed to delete account");
      setUpdateLoading(false);
    }
  };

  // Prevent form resubmission on page refresh
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (updateLoading) {
        // Cancel the event
        e.preventDefault();
        // Chrome requires returnValue to be set
        e.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [updateLoading]);
  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <ProfileForm
            initialFormData={formData}
            user={user}
            onUserUpdate={handleUserUpdate}
          />
        );
      case "address":
        // Redirect to address page
        return <AddressPage />;
      case "admin":
        return <AdminPanel currentUser={user} />;
      case "balance":
        return <BalanceForm user={user} />;
      case "balance-requests":
        return <BalanceRequestsManagement />;
      case "price-request":
        return <PriceRequestForm />;
      case "price-requests":
        return <PriceRequestsManagement />;
      case "transactions":
        return <TransactionsManagement />;
      case "password":
        return (
          <PasswordForm
            onSubmit={handlePasswordUpdate}
            loading={updateLoading}
          />
        );

      case "settings":
        return (
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            {/* Header */}
            <div className="bg-black px-6 py-4">
              <h3 className="text-xl font-bold text-white">إعدادات الحساب</h3>
              <p className="text-sm text-gray-100 mt-1">إدارة تفضيلات حسابك</p>
            </div>

            <div className="p-6">
              {/* Admin Panel Link */}
              {user && user.is_email_verified && (
                <div className="border border-gray-200 rounded-xl overflow-hidden mb-6">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-800">
                      لوحة الإدارة
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      الوصول إلى الميزات الإدارية
                    </p>
                  </div>

                  <div className="p-6">
                    <p className="text-gray-700 mb-4">
                      لديك صلاحيات إدارية. ادخل إلى لوحة الإدارة لإدارة
                      المستخدمين والتصاريح.
                    </p>
                    <button
                      onClick={() => router.push("/admin")}
                      className="btn-nursery"
                    >
                      الذهاب إلى لوحة الإدارة
                    </button>
                  </div>
                </div>
              )}

              {/* Delete Account Section */}
              <div className="border border-red-200 rounded-xl overflow-hidden">
                <div className="bg-red-50 px-6 py-4 border-b border-red-200">
                  <h4 className="text-lg font-semibold text-red-800">
                    منطقة الخطر
                  </h4>
                  <p className="text-sm text-red-600 mt-1">
                    إجراءات لا يمكن التراجع عنها تؤثر على حسابك
                  </p>
                </div>

                <div className="p-6">
                  <h5 className="text-lg font-semibold text-red-700 mb-3">
                    حذف الحساب
                  </h5>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-700 text-sm font-medium">
                      ⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع
                      بياناتك بشكل دائم.
                    </p>
                  </div>

                  <div className="space-y-4">
                    <input
                      type="password"
                      value={deletePassword}
                      onChange={(e) => setDeletePassword(e.target.value)}
                      placeholder="أدخل كلمة المرور لتأكيد الحذف"
                      className="w-full py-3 px-4 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:border-red-500"
                    />
                    <button
                      onClick={() => {
                        const formData = new FormData();
                        formData.append("password", deletePassword);
                        handleDeleteAccount(formData);
                      }}
                      disabled={!deletePassword || updateLoading}
                      className="w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-base font-semibold text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {updateLoading ? "جاري حذف الحساب..." : "حذف الحساب"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Navigation handler for sidebar
  const handleNavigation = (tab: string) => {
    setActiveTab(tab);
    localStorage.setItem("userAccountActiveTab", tab);
  };

  // If still checking authentication, show loading state
  if (isAuthenticating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner-nursery mx-auto mb-4"></div>
          <p className="text-gray-600">{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8 px-4 sm:px-6 lg:px-8"
      dir="rtl"
    >
      <div className="max-w-7xl mx-auto">
        {/* Page Header */}
        <div>
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              حسابي
            </h1>
            <p className="text-gray-600 text-lg">
              إدارة معلوماتك الشخصية وإعدادات حسابك
            </p>
          </div>

          {/* Summary Cards */}
          <SummaryCards className="mb-8" userCurrency={user?.user_currency} />
        </div>

        {/* Email Verification Notification */}
        {user && !user.is_email_verified && (
          <div className="mb-8">
            <div
              className="bg-red-50 border border-red-200 rounded-xl p-6"
              dir="rtl"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-400 rounded-full flex items-center justify-center">
                    <span className="text-red-800 font-bold">✉</span>
                  </div>
                </div>
                <div className="mr-3 flex-1">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    تأكيد البريد الإلكتروني مطلوب
                  </h3>
                  <p className="text-red-700 mb-4">
                    يرجى تأكيد بريدك الإلكتروني لتتمكن من استخدام جميع ميزات
                    الموقع بشكل كامل.
                  </p>
                  <div className="mt-4">
                    <button
                      onClick={() =>
                        router.push(
                          `/activate-account?email=${encodeURIComponent(
                            user.email
                          )}`
                        )
                      }
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                    >
                      تأكيد البريد الإلكتروني
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Profile Completion Message */}
        {user && !isProfileComplete(user) && (
          <div className="mb-8">
            <div
              className="bg-yellow-50 border border-yellow-200 rounded-xl p-6"
              dir="rtl"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-yellow-800 font-bold">!</span>
                  </div>
                </div>
                <div className="mr-3 flex-1">
                  <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                    أكمل ملفك الشخصي
                  </h3>
                  <p className="text-yellow-700 mb-4">
                    يرجى إكمال جميع معلومات ملفك الشخصي لتتمكن من استخدام جميع
                    ميزات الموقع.
                  </p>
                  <div className="space-y-2">
                    {(!user.first_name || !user.last_name) && (
                      <p className="text-sm text-yellow-600">
                        • أكمل المعلومات الأساسية
                      </p>
                    )}
                    {!user.phone_number && (
                      <p className="text-sm text-yellow-600">
                        • أضف رقم الهاتف
                      </p>
                    )}
                    {!isAddressComplete(user) && (
                      <p className="text-sm text-yellow-600">
                        • أضف معلومات العنوان
                      </p>
                    )}
                  </div>
                  <div className="mt-4 flex flex-wrap gap-3">
                    {(!user.first_name ||
                      !user.last_name ||
                      !user.phone_number) && (
                      <button
                        onClick={() => handleNavigation("profile")}
                        className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm font-medium"
                      >
                        تحديث الملف الشخصي
                      </button>
                    )}
                    {!isAddressComplete(user) && (
                      <button
                        onClick={() => handleNavigation("address")}
                        className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium"
                      >
                        إضافة العنوان
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="w-full flex items-center justify-center px-4 py-2 bg-white rounded-lg shadow-md hover:bg-gray-50 transition-colors"
            >
              {isSidebarOpen ? (
                <X className="w-5 h-5 ml-2" />
              ) : (
                <Menu className="w-5 h-5 ml-2" />
              )}
              <span>{isSidebarOpen ? "إغلاق القائمة" : "فتح القائمة"}</span>
            </button>
          </div>

          {/* Mobile Sidebar */}
          <div
            className={`
              lg:hidden
              ${isSidebarOpen ? "block" : "hidden"}
              w-full mb-4
            `}
          >
            <Sidebar
              activeTab={activeTab}
              onTabChange={(tab) => {
                setActiveTab(tab);
                setIsSidebarOpen(false);
              }}
            />
          </div>

          {/* Desktop Sidebar */}
          <div className="hidden lg:block lg:w-80 shrink-0">
            <Sidebar
              activeTab={activeTab}
              onTabChange={(tab) => {
                setActiveTab(tab);
                setIsSidebarOpen(false);
              }}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {loading ? (
              <div className="card-nursery p-8">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="spinner-nursery mx-auto mb-4"></div>
                    <p className="text-gray-600">{t("common.loading")}</p>
                  </div>
                </div>
              </div>
            ) : error ? (
              <div className="card-nursery p-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-red-700 font-medium">{error}</div>
                </div>
              </div>
            ) : (
              renderContent()
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;
