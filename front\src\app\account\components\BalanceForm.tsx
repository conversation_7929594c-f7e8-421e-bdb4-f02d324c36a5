"use client";

import React, { useState } from "react";
import { toast } from "react-toastify";
import { CreditCard, Loader2, Upload, Copy, CheckCircle } from "lucide-react";
import Input from "@/components/common/Input";

interface BalanceFormData {
  name: string;
  amount: string;
  notes: string;
  transactionFile: File | null;
}

interface FormErrors {
  name?: string;
  amount?: string;
  notes?: string;
  transactionFile?: string;
}

const BalanceForm: React.FC = ({ user }: { user: {phone} }) => {
  const [formData, setFormData] = useState<BalanceFormData>({
    name: "",
    amount: "",
    notes: "",
    transactionFile: null,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  // Bank account details
  const bankDetails = {
    bankName: "مصرف الراجحي - alrajhi bank",
    accountNumber: "395000010006086118542",
    accountName: "مؤسسة باب الامداد للتجارة",
    iban: "************************",
    swiftCode: "RJHISARI",
    sudanBankName: "بنك الخرطوم (بنكك)",
    sudanIban: "******************",
    sudanAccountNumber: "4153737",
    sudanAccountName: "محمد الفاتح عبدالرحمن جمعة فضل",
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "الاسم مطلوب";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "الاسم قصير جداً";
    }

    if (!formData.amount.trim()) {
      newErrors.amount = "المبلغ مطلوب";
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = "يرجى إدخال مبلغ صحيح";
    }

    if (!formData.transactionFile) {
      newErrors.transactionFile = "ملف التحويل مطلوب";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof BalanceFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          transactionFile: "حجم الملف يجب أن يكون أقل من 5 ميجابايت",
        }));
        return;
      }
      setFormData((prev) => ({ ...prev, transactionFile: file }));
      setErrors((prev) => ({ ...prev, transactionFile: undefined }));
      // Create preview for images only
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setImagePreview(null);
      }
    }
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      toast.success("تم نسخ البيانات بنجاح", {
        autoClose: 1500,
        hideProgressBar: true,
      });
      setTimeout(() => setCopiedField(null), 2000);
    } catch {
      toast.error("فشل في نسخ البيانات");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("يرجى تصحيح الأخطاء في النموذج");
      return;
    }

    setIsLoading(true);

    try {
      // Create FormData for file upload
      const submitData = new FormData();
      submitData.append("name", formData.name);
      submitData.append("amount", formData.amount);
      submitData.append("notes", formData.notes);
      if (formData.transactionFile) {
        submitData.append("transaction_file", formData.transactionFile);
      }

      // Submit to API
      const response = await fetch(
        "https://api.imdadport.com/api/balance/request/",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
          body: submitData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "فشل في إرسال طلب إضافة الرصيد");
      }

      toast.success("تم إرسال طلب إضافة الرصيد بنجاح", {
        autoClose: 3000,
        hideProgressBar: true,
      });

      // Reset form
      setFormData({
        name: "",
        amount: "",
        notes: "",
        transactionFile: null,
      });
      setImagePreview(null);
    } catch {
      toast.error("فشل في إرسال طلب إضافة الرصيد");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
      dir="rtl"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 to-black px-6 py-5">
        <h3 className="text-xl font-bold text-white">إضافة رصيد</h3>
        <p className="text-sm text-gray-300 mt-1">
          قم بتحويل المبلغ المطلوب وأرفق إيصال التحويل
        </p>
      </div>

      <div className="p-8 space-y-8">
        {/* Bank Details Section */}
        <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CreditCard className="w-5 h-5 ml-2" />
            بيانات الحساب البنكي
          </h4>

          <div className="flex flex-col gap-3">
            <div className="flex flex-wrap gap-3">
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                <div>
                  <p className="text-sm text-gray-600">اسم البنك (السعودية)</p>
                  <p className="font-medium text-gray-900">
                    {bankDetails.bankName}
                  </p>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(bankDetails.bankName, "bankName")
                  }
                  className="text-gray-500 hover:text-black transition-colors"
                >
                  {copiedField === "bankName" ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>

              <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                <div>
                  <p className="text-sm text-gray-600">رقم الحساب (السعودية)</p>
                  <p className="font-medium text-gray-900 font-mono">
                    {bankDetails.accountNumber}
                  </p>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(bankDetails.accountNumber, "accountNumber")
                  }
                  className="text-gray-500 hover:text-black transition-colors"
                >
                  {copiedField === "accountNumber" ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>

              <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                <div>
                  <p className="text-sm text-gray-600">
                    اسم صاحب الحساب (السعودية)
                  </p>
                  <p className="font-medium text-gray-900">
                    {bankDetails.accountName}
                  </p>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(bankDetails.accountName, "accountName")
                  }
                  className="text-gray-500 hover:text-black transition-colors"
                >
                  {copiedField === "accountName" ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
              <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                <div>
                  <p className="text-sm text-gray-600">
                    رقم الآيبان (السعودية)
                  </p>
                  <p className="font-medium text-gray-900 font-mono">
                    {bankDetails.iban}
                  </p>
                </div>
                <button
                  onClick={() => copyToClipboard(bankDetails.iban, "iban")}
                  className="text-gray-500 hover:text-black transition-colors"
                >
                  {copiedField === "iban" ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>

              <div className="flex justify-between items-center p-3 bg-white rounded-lg border w-full    md:w-[calc(50%-6px)]">
                <div>
                  <p className="text-sm text-gray-600">
                    رمز السويفت (السعودية)
                  </p>
                  <p className="font-medium text-gray-900 font-mono">
                    {bankDetails.swiftCode}
                  </p>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(bankDetails.swiftCode, "swiftCode")
                  }
                  className="text-gray-500 hover:text-black transition-colors"
                >
                  {copiedField === "swiftCode" ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            {user?.phone_number.startsWith("+966") && (
              <div className="flex flex-wrap gap-3">
                <div className="flex justify-between items-center p-3 bg-white rounded-lg border w-full  md:w-[calc(50%-6px)]">
                  <div>
                    <p className="text-sm text-gray-600">اسم البنك (السودان)</p>
                    <p className="font-medium text-gray-900">
                      {bankDetails.sudanBankName}
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        bankDetails.sudanBankName,
                        "sudanBankName"
                      )
                    }
                    className="text-gray-500 hover:text-black transition-colors"
                  >
                    {copiedField === "sudanBankName" ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>

                <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                  <div>
                    <p className="text-sm text-gray-600">
                      رقم الآيبان (السودان)
                    </p>
                    <p className="font-medium text-gray-900 font-mono">
                      {bankDetails.sudanIban}
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      copyToClipboard(bankDetails.sudanIban, "sudanIban")
                    }
                    className="text-gray-500 hover:text-black transition-colors"
                  >
                    {copiedField === "sudanIban" ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>

                <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                  <div>
                    <p className="text-sm text-gray-600">
                      رقم الحساب بنكك (السودان)
                    </p>
                    <p className="font-medium text-gray-900 font-mono">
                      {bankDetails.sudanAccountNumber}
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        bankDetails.sudanAccountNumber,
                        "sudanAccountNumber"
                      )
                    }
                    className="text-gray-500 hover:text-black transition-colors"
                  >
                    {copiedField === "sudanAccountNumber" ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>

                <div className="flex justify-between items-center p-3 bg-white rounded-lg border  w-full  md:w-[calc(50%-6px)]">
                  <div>
                    <p className="text-sm text-gray-600">
                      اسم صاحب الحساب (السودان)
                    </p>
                    <p className="font-medium text-gray-900">
                      {bankDetails.sudanAccountName}
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        bankDetails.sudanAccountName,
                        "sudanAccountName"
                      )
                    }
                    className="text-gray-500 hover:text-black transition-colors"
                  >
                    {copiedField === "sudanAccountName" ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Form Section */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              id="name"
              label="الاسم"
              type="text"
              placeholder="اسم المرسل كما يظهر في إيصال التحويل"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              error={!!errors.name}
              errorMessage={errors.name}
              required
            />

            <Input
              id="amount"
              label="المبلغ المحول"
              type="number"
              placeholder="مثال: 1000"
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", e.target.value)}
              error={!!errors.amount}
              errorMessage={errors.amount}
              required
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                ملف التحويل
                <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="file"
                  id="transactionFile"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <label
                  htmlFor="transactionFile"
                  className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                    errors.transactionFile
                      ? "border-red-300 bg-red-50"
                      : "border-gray-300 bg-gray-50 hover:bg-gray-100"
                  }`}
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-2 text-gray-500" />
                    <p className="text-sm text-gray-500">
                      {formData.transactionFile
                        ? formData.transactionFile.name
                        : "اختر ملف التحويل (صورة أو PDF أو أي نوع)"}
                    </p>
                  </div>
                </label>
              </div>
              {errors.transactionFile && (
                <p className="text-sm text-red-600">{errors.transactionFile}</p>
              )}
            </div>
          </div>

          {/* Image Preview */}
          {imagePreview && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                معاينة الصورة
              </label>
              <div className="relative inline-block">
                <img
                  src={imagePreview}
                  alt="معاينة إيصال التحويل"
                  className="max-w-xs max-h-48 rounded-lg border border-gray-300"
                />
                <button
                  type="button"
                  onClick={() => {
                    setFormData((prev) => ({
                      ...prev,
                      transactionFile: null,
                    }));
                    setImagePreview(null);
                  }}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors"
                >
                  ×
                </button>
              </div>
            </div>
          )}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              ملاحظات (اختياري)
            </label>
            <textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="أي ملاحظات إضافية حول التحويل..."
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>
          <div className="pt-8">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center items-center py-4 px-8 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-gray-900 to-black hover:from-gray-800 hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin ml-2" />
                  جاري الإرسال...
                </>
              ) : (
                "إرسال طلب إضافة الرصيد"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BalanceForm;
